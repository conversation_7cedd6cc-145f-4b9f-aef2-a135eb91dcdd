# 开发规范和规则

- 移除ToolExecutionAgent，其功能已分配给TrafficLogAgent(流量日志查询)和CodeExecutionAgent(代码执行)，需要更新相关Agent的system_message以承担原有的工具执行指导功能
- OpenSearch查询工具已简化：移除所有特殊时间字段检测和索引处理，固定使用firstPacket字段进行时间过滤，保留通用查询构建工具(query_traffic_logs, build_opensearch_query, build_range_condition, build_time_range_condition, validate_query_syntax)，支持2019年和2025年混合数据的统一查询分析
- OpenSearch查询工具最终简化：只保留一个万能查询接口query_traffic_logs，支持简单文本查询、字段匹配、复杂条件、通配符等多种查询格式，内部自动处理时间过滤(使用firstPacket字段)、查询构建、语法验证等复杂逻辑，Agent只需调用一个接口即可完成所有查询需求
- OpenSearch查询工具验证完成：8/8任务全部通过，支持所有分析任务清单需求(日志可用性验证、流量画像分析、网络层异常检测、应用层异常检测、时间维度分析、威胁情报关联、会话取证)，工具易用性优秀，错误处理完善，已创建详细使用指南docs/opensearch_query_guide.md
- mem0 telemetry错误已修复：更新了环境变量设置（MEM0_TELEMETRY=false, POSTHOG_DISABLED=1等），修正了mem0配置中的字段名（使用openai_base_url而不是base_url），mem0现在可以正常初始化和工作，记忆管理系统功能正常
- 配置重复问题彻底解决：移除了LOOP_CONTROL中的max_sub_analysis_count配置项，SubTaskManager现在直接使用GROUPCHAT.max_rounds配置，因为SubTaskManager.analysis_count和EnhancedGroupChatManager的协作轮次实际上是同一个概念。最终配置结构：LOOP_CONTROL只包含max_main_iterations(主流程循环)、context_compression_threshold(上下文压缩阈值)、progress_message_template(进度提示模板)；GROUPCHAT.max_rounds统一控制GroupChat对话轮数和子任务Agent分析次数，避免了功能重复
- 记忆系统已升级支持多种向量数据库：Qdrant（默认推荐）、Faiss（高性能）、ChromaDB、Pinecone（云）、Weaviate。通过环境变量VECTOR_STORE_TYPE选择，已移除telemetry相关代码，系统运行无警告。
- telemetry相关代码已完全清理：删除了src/utils/telemetry_disabler.py文件，移除了main_enhanced.py中的telemetry导入和调用，简化了.env.example中的telemetry设置。系统现在支持多种向量数据库（Qdrant、Faiss、ChromaDB、Pinecone、Weaviate），运行完全无警告。
- TaskManagerPersistence类在项目中未被实际使用，当前系统使用MemoryManager进行持久化管理，应移除TaskManagerPersistence类和相关的持久化方法以简化代码架构
- 完成了项目中所有旧的任务结构和记忆格式的彻底清理：1.将AgentExecutionRecord的conversation_type字段替换为message_type，统一使用user/assistant/system/tool四种消息类型；2.更新SubTaskManager.add_agent_conversation方法参数从conversation_type改为message_type；3.统一所有activity_type为message类型，移除tool_execution/data_collection等旧分类；4.更新统计和显示逻辑使用新的message_types；5.修复上下文提供者使用统一的role字段；6.更新测试文件同步新的字段名。验证结果：统一字段使用352处，统一结构使用112处，未发现旧模式残留，数据流保持一致性。
- 完成了数据结构的最终统一更新：1.将EnhancedGroupChatManager中的global_conversation字段完全替换为messages，包括GroupChatContext数据类、所有方法引用、返回结果字段名；2.更新所有相关的统计和显示逻辑使用新的messages字段；3.将activity_type参数重命名为record_type，统一为message类型；4.更新SubTaskManager.add_activity_record方法参数和所有调用点；5.更新测试文件将global_conversation添加到旧模式检查列表；6.验证结果显示统一字段使用352处，统一结构使用113处，旧模式残留0处，数据流一致性100%。整个系统现在拥有完全统一的消息格式：messages列表、message_type字段、record_type统一为message。
- AutoGen依赖修复：AutoGen 0.6.2需要opentelemetry-api==1.34.1和opentelemetry-semantic-conventions==0.55b1，已在requirements.txt中添加版本约束防止将来出现"cannot import name 'GEN_AI_TOOL_DESCRIPTION'"错误
