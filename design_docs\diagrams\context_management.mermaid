graph TB
    subgraph "混合记忆管理系统 (Hybrid Memory Management System)"
        subgraph "记忆工具层 (Memory Tools Layer)"
            MEM_TOOLS[记忆工具集<br/>MemoryTools<br/>🔧 工具化实现]
            CREATE_SESSION[创建Agent会话<br/>create_agent_memory_session]
            SAVE_INTERACTION[保存Agent交互<br/>save_agent_interaction]
            SEARCH_MEMORIES[搜索Agent记忆<br/>search_agent_memories]
            GET_CONTEXT[获取Agent上下文<br/>get_agent_context]
            STORE_CASE[存储分析案例<br/>store_analysis_case]
            SEARCH_GLOBAL[搜索全局记忆<br/>search_global_memories]
        end

        subgraph "记忆集成适配器 (Memory Integration Adapter)"
            MEM_ADAPTER[记忆适配器<br/>MemoryIntegrationAdapter<br/>🔗 统一接口]
            ACTIVE_SESSIONS[活跃会话映射<br/>Agent → Session ID]
            FORMAT_CONTEXT[格式化上下文<br/>format_context_for_agent]
        end

        subgraph "双层存储架构 (Dual Storage Architecture)"
            subgraph "本地JSON存储 (Local JSON Storage)"
                SESSION_MGR[会话管理器<br/>SessionManager<br/>📁 原始对话记录]
                JSON_FILES[JSON文件<br/>./memory_storage/sessions/<br/>📄 完整对话历史]
            end

            subgraph "Mem0向量记忆 (Mem0 Vector Memory)"
                MEM_PROC[记忆处理器<br/>MemoryProcessor<br/>🧠 智能记忆摘要]
                CHROMA_DB[ChromaDB<br/>./memory_storage/mem0_data/<br/>🔍 向量检索]
            end
        end
    end

    subgraph "5个专业化Agent (5 Specialized Agents)"
        PLANNING[PlanningAgent<br/>📋 计划制定专家<br/>🧠 deepseek-reasoner<br/>📝 记忆工具集成]
        REFLECTION[ReflectionAgent<br/>🔍 反思验证专家<br/>🧠 deepseek-reasoner<br/>📝 记忆工具集成]
        THREAT[ThreatAnalysisAgent<br/>🛡️ 威胁分析专家<br/>🧠 deepseek-reasoner<br/>📝 记忆工具集成]
        TOOL[ToolExecutionAgent<br/>🔧 工具执行专家<br/>💬 deepseek-chat<br/>📝 记忆工具集成]
        REPORT[ReportAgent<br/>📊 报告生成专家<br/>💬 deepseek-chat<br/>📝 记忆工具集成]
    end

    subgraph "Agent专用会话系统 (Agent-Specific Session System)"
        AGENT_SESSIONS[Agent专用会话<br/>独立记忆空间<br/>🔒 会话隔离]
        SESSION_MAPPING[会话映射<br/>Agent ID → Session ID<br/>🗺️ 自动管理]
        CONTEXT_BUILD[上下文构建<br/>个性化Agent上下文<br/>📝 智能筛选]
    end

    subgraph "智能记忆处理 (Intelligent Memory Processing)"
        AUTO_SUMMARY[自动摘要<br/>LLM驱动摘要生成<br/>🤖 智能压缩]
        SEMANTIC_SEARCH[语义检索<br/>Mem0向量相似度<br/>🔍 智能匹配]
        CONTEXT_FILTER[上下文筛选<br/>相关性+时间+频率<br/>⚖️ 智能权重]
        LENGTH_CONTROL[长度控制<br/>动态截断和压缩<br/>📏 智能调整]
    end

    %% 记忆工具层连接
    MEM_TOOLS --> CREATE_SESSION
    MEM_TOOLS --> SAVE_INTERACTION
    MEM_TOOLS --> SEARCH_MEMORIES
    MEM_TOOLS --> GET_CONTEXT
    MEM_TOOLS --> STORE_CASE
    MEM_TOOLS --> SEARCH_GLOBAL

    %% 记忆适配器连接
    MEM_TOOLS --> MEM_ADAPTER
    MEM_ADAPTER --> ACTIVE_SESSIONS
    MEM_ADAPTER --> FORMAT_CONTEXT

    %% 双层存储连接
    MEM_ADAPTER --> SESSION_MGR
    MEM_ADAPTER --> MEM_PROC
    SESSION_MGR --> JSON_FILES
    MEM_PROC --> CHROMA_DB

    %% Agent工具集成
    PLANNING --> MEM_TOOLS
    REFLECTION --> MEM_TOOLS
    THREAT --> MEM_TOOLS
    TOOL --> MEM_TOOLS
    REPORT --> MEM_TOOLS

    %% Agent会话系统
    MEM_ADAPTER --> AGENT_SESSIONS
    AGENT_SESSIONS --> SESSION_MAPPING
    SESSION_MAPPING --> CONTEXT_BUILD

    %% 智能记忆处理
    MEM_PROC --> AUTO_SUMMARY
    MEM_PROC --> SEMANTIC_SEARCH
    FORMAT_CONTEXT --> CONTEXT_FILTER
    CONTEXT_FILTER --> LENGTH_CONTROL

    %% 上下文构建流程
    CONTEXT_BUILD --> CONTEXT_FILTER
    LENGTH_CONTROL --> PLANNING
    LENGTH_CONTROL --> REFLECTION
    LENGTH_CONTROL --> THREAT
    LENGTH_CONTROL --> TOOL
    LENGTH_CONTROL --> REPORT

    %% 样式定义
    classDef memoryTools fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000000
    classDef memoryAdapter fill:#f1f8e9,stroke:#388e3c,stroke-width:2px,color:#000000
    classDef jsonStorage fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000000
    classDef vectorMemory fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000000
    classDef reasoningAgent fill:#ffecb3,stroke:#f57f17,stroke-width:3px,color:#000000
    classDef chatAgent fill:#c8e6c9,stroke:#388e3c,stroke-width:3px,color:#000000
    classDef sessionSystem fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000000
    classDef processing fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000000

    %% 应用样式
    class MEM_TOOLS,CREATE_SESSION,SAVE_INTERACTION,SEARCH_MEMORIES,GET_CONTEXT,STORE_CASE,SEARCH_GLOBAL memoryTools
    class MEM_ADAPTER,ACTIVE_SESSIONS,FORMAT_CONTEXT memoryAdapter
    class SESSION_MGR,JSON_FILES jsonStorage
    class MEM_PROC,CHROMA_DB vectorMemory
    class PLANNING,REFLECTION,THREAT reasoningAgent
    class TOOL,REPORT chatAgent
    class AGENT_SESSIONS,SESSION_MAPPING,CONTEXT_BUILD sessionSystem
    class AUTO_SUMMARY,SEMANTIC_SEARCH,CONTEXT_FILTER,LENGTH_CONTROL processing
