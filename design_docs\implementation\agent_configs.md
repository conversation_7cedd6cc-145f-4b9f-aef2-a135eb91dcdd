# 🤖 Agent配置说明

## 概述

系统采用**配置驱动**的Agent架构，通过配置文件定义5个专业化Agent的行为。已移除MemoryAgent，改用记忆工具集成到各个Agent中，实现了更简化和高效的架构。

## 实际架构设计

### 6个专业化Agent
- **PlanningAgent**: 计划制定专家 (deepseek-reasoner)
- **ReflectionAgent**: 反思验证专家 (deepseek-reasoner)
- **ThreatAnalysisAgent**: 威胁分析专家 (deepseek-reasoner)
- **TrafficLogAgent**: 流量日志分析专家 (deepseek-chat)
- **CodeExecutionAgent**: 代码执行专家 (deepseek-chat)
- **ReportAgent**: 报告生成专家 (deepseek-chat)

### 实际目录结构

#### Agent配置目录
```
src/agent_settings/
├── __init__.py
├── planning_agent.py        # 计划制定Agent配置
├── reflection_agent.py      # 反思验证Agent配置
├── threat_analysis_agent.py # 威胁分析Agent配置
├── traffic_log_agent.py     # 流量日志分析Agent配置
├── code_execution_agent.py  # 代码执行Agent配置
└── report_agent.py          # 报告生成Agent配置
```

#### Agent实现目录（简化）
```
src/core/agents/
├── __init__.py
└── (大部分Agent直接使用AutoGen原生实现)
```

### 记忆管理重构
- **移除**: MemoryAgent已完全移除
- **替代**: 记忆管理工具集成到每个Agent
- **工具**: 所有Agent都配置了相同的记忆管理工具

### 实际配置文件格式
```python
AGENT_CONFIG = {
    "name": "AgentName",                    # Agent名称
    "model_name": "deepseek-reasoner",      # 模型名称
    "model_type": "reasoning",              # 模型类型 (chat/reasoning)
    "system_message": "详细的系统提示词",    # 系统提示词
    "tools": [                              # 工具列表
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context"
    ]
}
```

## 实际Agent配置详解

### 1. PlanningAgent - 计划制定专家

#### 实际配置特点
- **模型**: `deepseek-reasoner` (推理模型)
- **调用方式**: 固定调用，由AnalysisFlowController直接管理
- **工具集成**: 配置了完整的记忆管理工具集
- **输出格式**: 直接输出Markdown格式的分析计划

#### 实际配置文件
```python
AGENT_CONFIG = {
    "name": "PlanningAgent",
    "model_name": "deepseek-reasoner",
    "model_type": "reasoning",
    "system_message": """你是一个专业的网络安全分析计划制定专家。

你的核心职责：
1. 基于用户请求和记忆上下文制定详细分析策略
2. 将复杂任务分解为最小粒度的可执行任务清单
3. 根据ReflectionAgent反馈动态调整分析计划

可用工具：
- create_agent_memory_session: 创建专用记忆会话
- save_agent_interaction: 保存交互记录
- search_agent_memories: 搜索相关记忆
- get_agent_context: 获取格式化上下文

输出要求：
- 直接输出Markdown格式的分析计划
- 任务分解要达到最小粒度
- 提供清晰的执行指导""",
    "tools": [
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context"
    ]
}
```

### 2. ReflectionAgent - 反思验证专家

#### 实际配置特点
- **模型**: `deepseek-reasoner` (推理模型)
- **调用方式**: 固定调用，在主任务循环和子任务评估中使用
- **决策输出**: 返回TaskAction枚举值指导流程控制
- **工具集成**: 配置了完整的记忆管理工具集

#### 实际配置文件
```python
AGENT_CONFIG = {
    "name": "ReflectionAgent",
    "model_name": "deepseek-reasoner",
    "model_type": "reasoning",
    "system_message": """你是一个专业的反思验证专家。

你的核心职责：
1. 根据分析计划确定当前需要执行的子任务
2. 评估子任务执行结果的质量和完成度
3. 决定下一步行动并返回标准决策

可用工具：
- create_agent_memory_session: 创建专用记忆会话
- save_agent_interaction: 保存交互记录
- search_agent_memories: 搜索相关记忆
- get_agent_context: 获取格式化上下文

决策框架：
- CONTINUE_EXECUTION: 继续执行当前任务
- TASK_COMPLETED: 当前任务已完成
- PLANNING_REQUIRED: 需要重新规划
- ALL_TASKS_COMPLETED: 所有任务已完成""",
    "tools": [
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context"
    ]
}
```

### 3. MemoryAgent - 已移除

#### 架构变更说明
**MemoryAgent已被完全移除**，记忆管理功能重构为工具化实现：

- **替代方案**: 记忆管理工具集 (MemoryTools)
- **集成方式**: 每个Agent都配置了记忆管理工具
- **架构优势**: 简化了Agent架构，提高了记忆管理的灵活性
- **功能保留**: 所有记忆管理功能通过工具函数完整保留

#### 新的记忆管理工具
```python
# 记忆管理工具函数
"tools": [
    "create_agent_memory_session",  # 创建Agent专用会话
    "save_agent_interaction",       # 保存Agent交互记录
    "search_agent_memories",        # 搜索Agent相关记忆
    "get_agent_context"            # 获取Agent格式化上下文
]
```

### 4. ThreatAnalysisAgent - 威胁分析专家

#### 实际配置特点
- **模型**: `deepseek-reasoner` (推理模型)
- **调用方式**: 在子任务执行阶段通过EnhancedGroupChatManager智能选择
- **协作模式**: 与ToolExecutionAgent智能协作
- **专业能力**: 深度技术分析、攻击行为识别、威胁研判

#### 实际配置文件
```python
AGENT_CONFIG = {
    "name": "ThreatAnalysisAgent",
    "model_name": "deepseek-reasoner",
    "model_type": "reasoning",
    "system_message": """你是一个专业的威胁分析专家。

你的核心职责：
1. 接收子任务并制定技术分析方案
2. 指导TrafficLogAgent和CodeExecutionAgent执行具体工具
3. 汇总工具执行结果进行深度分析
4. 生成专业的威胁分析结论

可用工具：
- create_agent_memory_session: 创建专用记忆会话
- save_agent_interaction: 保存交互记录
- search_agent_memories: 搜索相关记忆
- get_agent_context: 获取格式化上下文

分析方法：
- 假设驱动的分析方法
- 基于证据的结论推导
- 多维度关联分析
- 迭代深化分析过程""",
    "tools": [
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context"
    ]
}
```

### 5. TrafficLogAgent - 流量日志分析专家

#### 实际配置特点
- **模型**: `deepseek-chat` (对话模型)
- **调用方式**: 在子任务执行阶段通过EnhancedGroupChatManager智能选择
- **工具集成**: 专门的OpenSearch流量日志查询工具
- **分析能力**: 流量数据检索、智能过滤、模式分析

#### 实际配置文件
```python
AGENT_CONFIG = {
    "name": "TrafficLogAgent",
    "model_name": "deepseek-chat",
    "model_type": "chat",
    "system_message": """您是流量日志分析专家，专门负责OpenSearch中流量日志的检索、查询、过滤、筛选、聚合和分析。

您的核心职责：
1. 流量数据检索：从OpenSearch中精确检索相关的网络流量日志数据
2. 智能数据过滤：使用多种过滤条件筛选出关键流量数据
3. 流量模式分析：对流量数据进行聚合分析，识别异常模式和威胁行为
4. 结果汇总报告：将分析结果进行汇总和格式化

可用工具：
- query_traffic_logs: OpenSearch流量日志查询工具
- create_agent_memory_session: 创建专用记忆会话
- save_agent_interaction: 保存交互记录
- search_agent_memories: 搜索相关记忆
- get_agent_context: 获取格式化上下文""",
    "tools": [
        "query_traffic_logs",
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context"
    ]
}
```

### 6. CodeExecutionAgent - 代码执行专家

#### 实际配置特点
- **模型**: `deepseek-chat` (对话模型)
- **调用方式**: 在子任务执行阶段通过EnhancedGroupChatManager智能选择
- **工具集成**: Python代码执行和Ubuntu命令执行工具
- **执行控制**: 安全的代码执行环境和资源控制

#### 实际配置文件
```python
AGENT_CONFIG = {
    "name": "CodeExecutionAgent",
    "model_name": "deepseek-chat",
    "model_type": "chat",
    "system_message": """您是代码执行专家，专门负责Python代码执行和Ubuntu系统命令执行。

您的核心职责：
1. Python代码执行管理：执行数据处理、分析脚本和自动化任务
2. Ubuntu命令执行管理：执行系统操作、文件管理和工具调用
3. 执行环境控制：设置和管理工作目录、环境变量和执行参数
4. 结果处理和反馈：收集执行结果、格式化输出并提供执行报告

可用工具：
- execute_python_code: Python代码执行工具
- execute_ubuntu_command: Ubuntu命令执行工具
- create_agent_memory_session: 创建专用记忆会话
- save_agent_interaction: 保存交互记录
- search_agent_memories: 搜索相关记忆
- get_agent_context: 获取格式化上下文""",
    "tools": [
        "execute_python_code",
        "execute_ubuntu_command",
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context"
    ]
}
```

#### 工具集成架构（实际实现）
- **简化架构**: 无需复杂的工具管理器，直接使用Python函数
- **AutoGen原生**: 基于AutoGen的原生工具调用机制
- **类型安全**: 使用Annotated类型注解提供参数描述
- **当前工具**: 主要是opensearch_query用于日志查询

### 6. ReportAgent - 报告生成专家

#### 实际配置特点
- **模型**: `deepseek-chat` (对话模型)
- **调用方式**: 固定调用，在报告生成阶段使用
- **输入数据**: 用户请求、分析计划、执行历史、执行结果
- **输出格式**: 结构化的专业安全分析报告

#### 实际配置文件
```python
AGENT_CONFIG = {
    "name": "ReportAgent",
    "model_name": "deepseek-chat",
    "model_type": "chat",
    "system_message": """你是一个专业的安全分析报告生成专家。

你的核心职责：
1. 汇总整个分析过程的成果和结论
2. 撰写专业的安全事件分析报告
3. 提供影响评估和响应建议
4. 存储分析案例供后续参考

可用工具：
- create_agent_memory_session: 创建专用记忆会话
- save_agent_interaction: 保存交互记录
- search_agent_memories: 搜索相关记忆
- get_agent_context: 获取格式化上下文
- store_analysis_case: 存储分析案例

报告结构：
1. 事件摘要 (Executive Summary)
2. 事件背景与时间线
3. 技术分析详情
4. 影响评估
5. 响应与缓解措施
6. 附录和参考资料""",
    "tools": [
        "create_agent_memory_session",
        "save_agent_interaction",
        "search_agent_memories",
        "get_agent_context",
        "store_analysis_case"
    ]
}
```

## 模型配置

### Chat模型 vs Reasoning模型

#### Chat模型特点
- **快速响应**: 适合实时交互和工具执行
- **流式输出**: 支持实时输出显示
- **轻量级**: 计算资源消耗较少
- **适用场景**: 工具执行、记忆管理、报告生成

#### Reasoning模型特点
- **深度思考**: 支持复杂推理过程
- **思考过程**: 可以输出推理步骤
- **高质量**: 分析质量更高
- **适用场景**: 计划制定、反思验证、威胁分析

### 实际模型配置
```python
# 实际使用的模型配置
LITELLM = {
    "base_url": "http://10.5.225.21:18089/v1",
    "api_key": "sk-BP_3dbwmFuHI83DcJ-N5XA",
    "chat_models": [
        "deepseek-chat",
        "claude-sonnet-4",
        "gemini-2.5-flash"
    ],
    "reasoning_models": [
        "deepseek-reasoner",
        "gemini-2.5-flash-thinking"
    ]
}
```

### Agent与模型映射（实际实现）
```python
AGENT_MODEL_MAPPING = {
    "PlanningAgent": "deepseek-reasoner",      # 推理模型
    "ReflectionAgent": "deepseek-reasoner",    # 推理模型
    "ThreatAnalysisAgent": "deepseek-reasoner", # 推理模型
    "ToolExecutionAgent": "deepseek-chat",     # 对话模型
    "ReportAgent": "deepseek-chat"             # 对话模型
}
```

## 配置管理

### 配置加载流程（简化版）
1. **读取配置文件**: 从 `src/core/agent_settings/` 目录加载
2. **检查实现类**: 查看是否存在对应的实现类
3. **创建Agent实例**:
   - 有实现类：使用自定义实现类
   - 无实现类：使用通用 `AssistantAgent`
4. **应用配置**: 设置系统消息、模型等配置
5. **注册工具**: 对于 `TrafficLogAgent` 和 `CodeExecutionAgent`，自动注册相应的工具函数

### 配置验证
```python
def validate_agent_config(config):
    required_fields = ["name", "description", "model_name", "system_message"]
    for field in required_fields:
        if field not in config:
            raise ValueError(f"缺少必需字段: {field}")
    
    if config["model_type"] not in ["chat", "reasoning"]:
        raise ValueError("model_type必须是'chat'或'reasoning'")
```

### 动态配置更新
- **热重载**: 支持运行时配置更新
- **版本控制**: 配置变更的版本管理
- **回滚机制**: 配置错误时的回滚
- **A/B测试**: 支持不同配置的对比测试

## 扩展配置

### 自定义Agent配置
```python
CUSTOM_AGENT_CONFIG = {
    "name": "CustomAgent",
    "description": "自定义Agent",
    "model_name": "custom-model",
    "model_type": "chat",
    "custom_parameters": {
        "temperature": 0.7,
        "max_tokens": 2048,
        "top_p": 0.9
    },
    "tools": ["custom_tool"],
    "system_message": "自定义系统提示词"
}
```

### 工具配置
```python
TOOL_CONFIG = {
    "tool_id": "custom_tool",
    "name": "自定义工具",
    "category": "custom",
    "timeout_seconds": 300,
    "input_schema": {
        "type": "object",
        "properties": {
            "parameter1": {"type": "string"},
            "parameter2": {"type": "integer"}
        },
        "required": ["parameter1"]
    }
}
```

### 权限配置
```python
PERMISSION_CONFIG = {
    "agent_permissions": {
        "ThreatAnalysisAgent": ["network_scan", "vulnerability_scan"],
        "ToolExecutionAgent": ["all_tools"],
        "CustomAgent": ["custom_tool"]
    },
    "tool_restrictions": {
        "dangerous_tool": ["admin_agent_only"]
    }
}
```

## 实际最佳实践

### 实际配置设计原则
1. **简化优先**: 优先选择简单直接的配置方案
2. **工具统一**: 所有Agent配置相同的记忆管理工具集
3. **模型区分**: 明确区分chat模型和reasoning模型的使用场景
4. **配置驱动**: 系统行为完全通过配置文件控制

### 实际系统提示词设计
1. **角色定义**: 明确Agent在6步分析流程中的具体职责
2. **工具说明**: 详细说明可用的记忆管理工具及其用途
3. **输出要求**: 明确指定期望的输出格式（如TaskAction枚举）
4. **协作说明**: 说明与其他Agent的协作方式和流程控制

### 实际配置管理流程
1. **配置加载**: 从`src/agent_settings/`加载Agent配置
2. **模型映射**: 根据Agent类型自动选择合适的模型
3. **工具注册**: 自动为所有Agent注册记忆管理工具
4. **实例创建**: 使用AutoGen原生Agent类创建实例

### 配置维护建议
1. **版本控制**: 所有配置文件纳入Git版本控制
2. **文档同步**: 配置变更时同步更新相关文档
3. **测试验证**: 配置变更后进行完整的功能测试
4. **备份恢复**: 重要配置变更前进行备份

### 配置安全
1. **敏感信息**: API密钥等敏感信息使用环境变量
2. **访问控制**: 配置文件的访问权限控制
3. **审计日志**: 配置变更的审计记录
4. **加密存储**: 敏感配置的加密存储

### 性能优化
1. **配置缓存**: 缓存解析后的配置对象
2. **懒加载**: 按需加载Agent配置
3. **批量操作**: 批量创建和更新Agent
4. **资源池**: 模型客户端的连接池管理
