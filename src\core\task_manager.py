"""
任务管理系统

实现主任务管理器和子任务管理器，支持任务创建、状态跟踪、结果汇总等功能。
按照README要求：
- 主任务管理器：管理用户指令、PlanningAgent制定的整体任务清单、子任务列表、执行结果、上下文记忆、参考模板案例等
- 子任务管理器：管理当前执行的子任务的记忆、上下文、工具执行、数据收集等
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from src.utils.logger import get_logger
from dataclasses import dataclass, asdict, field
from src.extensions.memory.memory_processor import MemoryProcessor


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"           # ⏳ 待处理
    IN_PROGRESS = "in_progress"   # 🔄 处理中
    COMPLETED = "completed"       # ✅ 已完成
    FAILED = "failed"            # ❌ 失败
    CANCELLED = "cancelled"      # 🚫 已取消
    BLOCKED = "blocked"          # ⚠️ 阻塞


class TaskPriority(Enum):
    """任务优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TaskItem:
    """任务项数据结构"""
    task_id: str
    title: str
    description: str
    status: TaskStatus
    priority: TaskPriority = TaskPriority.MEDIUM
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    assigned_agent: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)  # 依赖的任务ID列表
    execution_result: Optional[str] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    

    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime序列化
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, (TaskStatus, TaskPriority)):
                data[key] = value.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskItem':
        """从字典创建TaskItem"""
        # 处理datetime反序列化
        for key in ['created_at', 'updated_at', 'completed_at']:
            if data.get(key):
                data[key] = datetime.fromisoformat(data[key])
        
        # 处理枚举反序列化
        if 'status' in data:
            data['status'] = TaskStatus(data['status'])
        if 'priority' in data:
            data['priority'] = TaskPriority(data['priority'])
        
        return cls(**data)


class BaseTaskManager:
    """任务管理器基类"""
    
    def __init__(self, manager_id: str, name: str):
        self.manager_id = manager_id
        self.name = name
        self.tasks: Dict[str, TaskItem] = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def create_task(self, title: str, description: str, priority: TaskPriority = TaskPriority.MEDIUM,
                   assigned_agent: Optional[str] = None, dependencies: Optional[List[str]] = None) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        task = TaskItem(
            task_id=task_id,
            title=title,
            description=description,
            status=TaskStatus.PENDING,
            priority=priority,
            assigned_agent=assigned_agent,
            dependencies=dependencies or []
        )
        
        self.tasks[task_id] = task
        self.updated_at = datetime.now()
        return task_id
    
    def get_task(self, task_id: str) -> Optional[TaskItem]:
        """获取任务"""
        return self.tasks.get(task_id)
    



class MainTaskManager(BaseTaskManager):
    """
    主任务管理器

    管理用户指令、PlanningAgent制定的整体任务清单、子任务列表、执行结果等
    注意：记忆管理统一使用MemoryManager，不在此重复实现
    """

    def __init__(self, session_id: str, user_request: str):
        super().__init__(f"main_{session_id}", f"主任务管理器_{session_id}")
        self.session_id = session_id
        self.user_request = user_request
        self.analysis_plan: Optional[str] = None
        self.sub_task_managers: Dict[str, 'SubTaskManager'] = {}
        self.execution_results: List[Dict[str, Any]] = []
        self.final_report: Optional[str] = None
        self.logger = get_logger(f"main_task_{session_id[:8]}")

        # 从配置文件加载循环控制设置
        from src.config import get_config
        loop_config = get_config("loop_control")
        self.main_analysis_count: int = 0
        self.max_main_analysis_count: int = loop_config.get("max_main_iterations", 10)
        self.progress_message_template: str = loop_config.get("progress_message_template",
            "当前{context}，你最多只能分析{max_count}次，现在已经是第{current}次分析")

        self.logger.info(f"创建主任务管理器: {session_id}, 用户请求: {user_request[:100]}...")

    def set_analysis_plan(self, plan: str):
        """设置分析计划"""
        self.analysis_plan = plan
        self.updated_at = datetime.now()

    def set_final_report(self, report: str):
        """设置最终报告"""
        self.final_report = report
        self.updated_at = datetime.now()



    def create_sub_task_manager(self, task_id: str, task_title: str) -> 'SubTaskManager':
        """为特定任务创建子任务管理器"""
        sub_manager = SubTaskManager(
            main_task_id=task_id,
            task_title=task_title,
            parent_session_id=self.session_id
        )
        self.sub_task_managers[task_id] = sub_manager
        self.updated_at = datetime.now()
        return sub_manager



    def get_main_flow_context(self, agent_role: str) -> Dict[str, Any]:
        """为主流程Agent获取完整上下文信息（优化版本）"""
        # 增加主流程分析次数
        self.main_analysis_count += 1

        return {
            "session_info": {
                "session_id": self.session_id,
                "user_request": self.user_request,
                "analysis_plan": self.analysis_plan,
                # 新增：主流程分析进度
                "analysis_progress": {
                    "current_count": self.main_analysis_count,
                    "max_count": self.max_main_analysis_count,
                    "remaining": self.max_main_analysis_count - self.main_analysis_count,
                    "progress_message": self.progress_message_template.format(
                        context="主流程分析",
                        max_count=self.max_main_analysis_count,
                        current=self.main_analysis_count
                    )
                }
            },
            # 新增：markdown格式的任务清单
            "task_list_markdown": self.analysis_plan if self.analysis_plan else "暂无任务清单",
            "task_list": [
                {
                    "task_id": task.task_id,
                    "title": task.title,
                    "description": task.description,
                    "status": task.status.value,
                    "completed": task.status == TaskStatus.COMPLETED
                }
                for task in self.tasks.values()
            ],
            "execution_history": self.execution_results[-5:],  # 最近5个执行结果
            "sub_task_summaries": [
                {
                    "task_id": sub_mgr.main_task_id,
                    "title": sub_mgr.task_title,
                    "activity_count": len(sub_mgr.activity_history),
                    "context_summary": sub_mgr.context_summary,
                    "analysis_count": sub_mgr.analysis_count,  # 新增：子任务分析次数
                    "max_analysis_count": sub_mgr.max_analysis_count
                }
                for sub_mgr in self.sub_task_managers.values()
            ]
        }

    def get_task_status_summary(self) -> Dict[str, Any]:
        """获取任务状态摘要（替代旧的get_progress_summary方法）"""
        # 统计任务状态
        status_counts = {}
        for status in TaskStatus:
            status_counts[status.value] = 0

        for task in self.tasks.values():
            status_counts[task.status.value] += 1

        # 计算进度
        total_tasks = len(self.tasks)
        completed_tasks = status_counts.get("completed", 0)
        progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        return {
            "total_tasks": total_tasks,
            "status_counts": status_counts,
            "progress_percentage": progress_percentage,
            "analysis_progress": {
                "current_count": self.main_analysis_count,
                "max_count": self.max_main_analysis_count,
                "remaining": self.max_main_analysis_count - self.main_analysis_count
            },
            "sub_task_count": len(self.sub_task_managers),
            "execution_results_count": len(self.execution_results),
            "has_final_report": bool(self.final_report)
        }


class SubTaskManager(BaseTaskManager):
    """
    子任务管理器

    管理当前执行的子任务的工具执行、数据收集等
    注意：记忆和上下文管理统一使用MemoryManager，不在此重复实现
    """

    def __init__(self, main_task_id: str, task_title: str, parent_session_id: str):
        super().__init__(f"sub_{main_task_id}", f"子任务管理器_{task_title}")
        self.main_task_id = main_task_id
        self.task_title = task_title
        self.parent_session_id = parent_session_id
        # 统一的活动历史记录（保持时间顺序）
        self.activity_history: List[Dict[str, Any]] = []  # 统一的活动历史（对话、工具执行、数据收集等）
        self.context_summary: str = ""  # 上下文摘要

        # 从配置文件加载循环控制设置
        from src.config import get_config
        loop_config = get_config("loop_control")
        groupchat_config = get_config("groupchat")
        self.analysis_count: int = 0  # 当前分析次数（与GroupChat轮次同步）
        self.max_analysis_count: int = groupchat_config.get("max_rounds", 10)  # 使用GroupChat的max_rounds
        self.context_compression_threshold: int = loop_config.get("context_compression_threshold", 5000)
        self.progress_message_template: str = loop_config.get("progress_message_template",
            "当前{context}，你最多只能分析{max_count}次，现在已经是第{current}次分析")

        # 新增：LLM摘要生成器
        self.memory_processor = MemoryProcessor()

    def add_activity_record(self, record_type: str = "message", agent_name: str = "",
                           content: str = "", role: str = "assistant",
                           tool_call: Optional[Dict[str, Any]] = None,
                           tool_response: Optional[Dict[str, Any]] = None,
                           reasoning: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None):
        """
        统一的活动记录方法，使用与记忆管理器一致的格式

        Args:
            record_type: 记录类型 (统一为 "message")
            agent_name: Agent名称
            content: 主要内容
            role: 角色 (user, assistant, system, tool)
            tool_call: 工具调用信息
            tool_response: 工具响应信息
            reasoning: 推理过程
            metadata: 其他元数据
        """
        # 增加分析次数（对所有消息类型）
        self.analysis_count += 1

        # 自动生成消息摘要
        message_summary = self._generate_activity_summary(
            agent_name, content, tool_call, reasoning
        )

        # 创建统一格式的活动记录（与ConversationMessage格式一致）
        activity_record = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "reasoning": reasoning,
            "tool_call": tool_call,
            "tool_response": tool_response,
            "message_summary": message_summary,
            "metadata": metadata or {},

            # Agent和任务信息
            "agent_name": agent_name,
            "agent_role": metadata.get("agent_role", "") if metadata else "",
            "task_type": "sub_task",
            "task_id": self.main_task_id,
            "task_title": self.task_title,

            # 消息特定信息
            "record_type": record_type,  # 统一为message类型
            "analysis_sequence": self.analysis_count,
            "execution_duration": metadata.get("execution_duration", 0.0) if metadata else 0.0
        }

        # 添加到统一活动历史
        self.activity_history.append(activity_record)

        self.updated_at = datetime.now()

    def add_tool_execution(self, tool_name: str, parameters: Dict[str, Any],
                          result: str, status: str = "success", agent_name: str = ""):
        """记录工具执行（使用统一格式）"""
        tool_call = {
            "name": tool_name,
            "parameters": parameters,
            "status": status
        }
        tool_response = {
            "result": result,
            "status": status
        }

        content = f"执行工具: {tool_name}"
        if status != "success":
            content += f" (状态: {status})"

        self.add_activity_record(
            record_type="message",
            agent_name=agent_name,
            content=content,
            role="tool",  # 工具消息使用tool角色
            tool_call=tool_call,
            tool_response=tool_response,
            metadata={"tool_name": tool_name, "parameters": parameters}
        )

    def add_data_collection(self, data_type: str, source: str, data: Any,
                           metadata: Optional[Dict[str, Any]] = None, agent_name: str = ""):
        """记录数据收集（使用统一格式）"""
        content = f"收集数据: {data_type} (来源: {source})"

        collection_metadata = metadata or {}
        collection_metadata.update({
            "data_type": data_type,
            "source": source,
            "data": data
        })

        self.add_activity_record(
            record_type="message",
            agent_name=agent_name,
            content=content,
            role="assistant",  # 数据收集仍使用assistant角色
            metadata=collection_metadata
        )

    def add_agent_conversation(self, agent_name: str, agent_role: str, message_type: str,
                              input_message: str, output_message: str, reasoning_content: str = "",
                              tool_calls: Optional[List[Dict[str, Any]]] = None,
                              execution_duration: float = 0.0):
        """记录Agent对话历史（使用统一格式）"""

        # 构建工具调用信息（如果有）
        tool_call = None
        tool_response = None
        if tool_calls:
            tool_call = {
                "calls": tool_calls,
                "count": len(tool_calls)
            }
            # 从tool_calls中提取响应信息
            responses = []
            for call in tool_calls:
                if "result" in call or "response" in call:
                    responses.append({
                        "tool": call.get("name", "unknown"),
                        "result": call.get("result", call.get("response", ""))
                    })
            if responses:
                tool_response = {"responses": responses}

        # 构建内容
        content = output_message
        if not content and tool_calls:
            content = f"执行了 {len(tool_calls)} 个工具调用"

        # 构建元数据
        metadata = {
            "agent_role": agent_role,
            "message_type": message_type,
            "input_message": input_message,
            "output_message": output_message,
            "execution_duration": execution_duration,
            "parent_session": self.parent_session_id
        }

        self.add_activity_record(
            record_type="message",  # 统一为message类型
            agent_name=agent_name,
            content=content,
            role=message_type,  # 使用message_type作为role
            tool_call=tool_call,
            tool_response=tool_response,
            reasoning=reasoning_content,
            metadata=metadata
        )

    def _generate_activity_summary(self, agent_name: str, content: str,
                                  tool_call: Optional[Dict[str, Any]] = None,
                                  reasoning: Optional[str] = None) -> str:
        """生成活动摘要（统一方法）"""
        # 直接使用简单摘要逻辑，避免LLM超时问题
        # 根据工具调用和推理内容判断消息类型
        if tool_call and "name" in tool_call:
            return f"{agent_name}执行工具: {tool_call['name']}"
        elif reasoning:
            return f"{agent_name}推理分析"
        else:
            action = "消息交互"

        result = content[:50].replace('\n', ' ').strip() if content else ""
        if len(content) > 50:
            result += "..."

        return f"{agent_name}{action}。{result}"



    def update_context_summary(self, summary: str):
        """更新上下文摘要"""
        self.context_summary = summary
        self.updated_at = datetime.now()



    def _get_optimized_activity_history(self) -> List[Dict[str, Any]]:
        """获取优化的统一活动历史（保持时间顺序，使用摘要控制长度）"""
        if not self.activity_history:
            return []

        # 按时间戳排序确保顺序正确
        sorted_activities = sorted(self.activity_history, key=lambda x: x.get('timestamp', ''))

        # 计算当前上下文长度（粗略估算）
        total_length = 0
        for activity in sorted_activities:
            total_length += len(activity.get('content', ''))
            if activity.get('reasoning'):
                total_length += len(activity['reasoning'])

        # 如果上下文长度超过阈值，使用摘要
        if total_length > self.context_compression_threshold:
            # 保留最近10条的完整信息，其他使用摘要
            recent_activities = sorted_activities[-10:]
            older_activities = sorted_activities[:-10]

            # 为较老的活动创建摘要版本
            summarized_activities = []
            for activity in older_activities:
                summarized_activity = {
                    "timestamp": activity["timestamp"],
                    "agent_name": activity.get("agent_name", ""),
                    "agent_role": activity.get("agent_role", ""),
                    "record_type": activity.get("record_type", "message"),
                    "message_summary": activity.get("message_summary", f"{activity.get('agent_name', 'Unknown')}执行{activity.get('record_type', 'message')}"),
                    "analysis_sequence": activity.get("analysis_sequence", 0),
                    "execution_duration": activity.get("execution_duration", 0),
                    "is_summarized": True  # 标记为摘要版本
                }
                summarized_activities.append(summarized_activity)

            return summarized_activities + recent_activities
        else:
            return sorted_activities







