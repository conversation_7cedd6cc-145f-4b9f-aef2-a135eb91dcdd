import abc
import asyncio
from autogen_agentchat.agents import UserProxyAgent # For type hinting or base class if useful
# import chainlit as cl # Removed
# from src.config import get_config # Removed as only ChainlitUserProxy used it

class BaseUserProxy(abc.ABC):
    @abc.abstractmethod
    async def get_input(self, prompt: str) -> str:
        """获取用户输入。可以是异步的。"""
        pass 

# ChainlitUserProxy class removed entirely

class CommandLineUserProxy(UserProxyAgent, BaseUserProxy):
    """命令行用户代理Agent"""

    def __init__(self, name: str = "cli_user_proxy"):
        super().__init__(name=name)

    async def get_input(self, prompt: str) -> str:
        """通过命令行获取用户输入。"""
        print(prompt) 
        loop = asyncio.get_event_loop()
        try:
            user_input = await loop.run_in_executor(None, input, "您的回复: ")
            return user_input if user_input is not None else ""
        except Exception as e:
            print(f"获取命令行输入时出错: {e}")
            return "" 