"""
信息收集工具实现 - 简化版

提供OpenSearch流量日志查询和分析工具，支持从arkime索引中查询、过滤、筛选流量日志数据。
这些工具被定义为可由AutoGen Agent直接调用的Python函数。

简化原则：
- 使用固定的时间字段 firstPacket 进行时间过滤
- 移除所有特殊索引处理和自动检测
- 保持通用的查询构建工具
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Literal, Callable
from typing_extensions import Annotated
from src.utils.logger import get_logger

logger = get_logger("information_collection_tools")

try:
    from opensearchpy import OpenSearch
    from opensearchpy.exceptions import OpenSearchException
except ImportError:
    logger.warning("警告: opensearch-py 未安装，请运行: pip install opensearch-py")
    OpenSearch = None
    OpenSearchException = Exception


class OpenSearchClient:
    """OpenSearch客户端管理类"""
    
    def __init__(self):
        self.client = None
        self.index_name = 'arkime*'
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化OpenSearch客户端"""
        if OpenSearch is None:
            raise ImportError("opensearch-py 未安装")
            
        try:
            self.client = OpenSearch(
                hosts=[{'host': '************', 'port': 9200}],
                http_auth=('admin', 'Open-1024'),
                use_ssl=False,
                verify_certs=False,
                timeout=30
            )
            # 测试连接
            self.client.info()
            logger.info("✅ OpenSearch连接成功")
        except Exception as e:
            logger.error(f"❌ OpenSearch连接失败: {e}")
            self.client = None
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.client is not None


# 全局客户端实例
_opensearch_client = OpenSearchClient()


def _save_query_results(results: Dict[str, Any], query_type: str) -> str:
    """
    保存查询结果到本地文件
    
    :param results: 查询结果
    :param query_type: 查询类型
    :return: 保存的文件路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"opensearch_query_{query_type}_{timestamp}.json"
    filepath = os.path.join("data", "query_results", filename)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    logger.info(f"📁 查询结果已保存到: {filepath}")
    return filepath


def _generate_summary(hits: List[Dict], total_hits: int) -> Dict[str, Any]:
    """
    生成查询结果汇总信息
    
    :param hits: 查询命中的文档列表
    :param total_hits: 总命中数
    :return: 汇总信息字典
    """
    if not hits:
        return {"total_hits": total_hits, "summary": "无数据"}
    
    # 统计协议分布
    protocols = {}
    source_ips = set()
    dest_ips = set()
    
    for hit in hits:
        source = hit.get('_source', {})
        
        # 协议统计
        protocol = source.get('protocol', 'unknown')
        if isinstance(protocol, list):
            for p in protocol:
                protocols[p] = protocols.get(p, 0) + 1
        else:
            protocols[protocol] = protocols.get(protocol, 0) + 1
        
        # IP统计
        if 'source' in source and 'ip' in source['source']:
            src_ip = source['source']['ip']
            if isinstance(src_ip, list):
                source_ips.update(src_ip)
            elif src_ip:
                source_ips.add(src_ip)
        if 'destination' in source and 'ip' in source['destination']:
            dst_ip = source['destination']['ip']
            if isinstance(dst_ip, list):
                dest_ips.update(dst_ip)
            elif dst_ip:
                dest_ips.add(dst_ip)
    
    summary = {
        "total_hits": total_hits,
        "returned_hits": len(hits),
        "protocol_distribution": protocols,
        "unique_source_ips": len(source_ips),
        "unique_dest_ips": len(dest_ips),
        "time_range": {
            "earliest": None,
            "latest": None
        }
    }
    
    # 时间范围统计
    timestamps = []
    for hit in hits:
        source = hit.get('_source', {})
        if 'firstPacket' in source:
            timestamps.append(source['firstPacket'])
    
    if timestamps:
        timestamps.sort()
        summary["time_range"]["earliest"] = timestamps[0]
        summary["time_range"]["latest"] = timestamps[-1]
    
    return summary


def _parse_datetime_to_timestamp(date_str: str) -> Optional[int]:
    """
    将日期时间字符串转换为毫秒时间戳

    :param date_str: 日期时间字符串，支持多种格式
    :return: 毫秒时间戳，失败返回None
    """
    # 支持的日期格式
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%SZ"
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(date_str.strip(), fmt)
            return int(dt.timestamp() * 1000)  # 转换为毫秒时间戳
        except ValueError:
            continue

    logger.warning(f"无法解析时间格式: {date_str}")
    return None


def _build_time_filter(time_range: str) -> Optional[Dict]:
    """
    构建时间范围过滤器，使用firstPacket字段进行时间过滤

    arkime数据使用毫秒时间戳格式，需要将输入的时间字符串转换为毫秒时间戳

    :param time_range: 时间范围，支持多种格式：
                      - 相对时间: "1h", "24h", "7d", "30m"
                      - 绝对时间: "2019-07-01 to 2019-07-07"
                      - 精确时间: "2019-07-16 05:58:52 to 2019-07-16 06:58:52"
    :return: 时间过滤器字典
    """
    if not time_range:
        return None

    # 使用firstPacket作为主要时间字段，这是arkime中数据包的实际时间
    time_field = "firstPacket"
    now = datetime.now()

    # 处理相对时间范围
    if time_range.endswith('h'):
        hours = int(time_range[:-1])
        start_time = now - timedelta(hours=hours)
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)
        return {
            "range": {
                time_field: {
                    "gte": start_timestamp,
                    "lte": end_timestamp
                }
            }
        }
    elif time_range.endswith('d'):
        days = int(time_range[:-1])
        start_time = now - timedelta(days=days)
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)
        return {
            "range": {
                time_field: {
                    "gte": start_timestamp,
                    "lte": end_timestamp
                }
            }
        }
    elif time_range.endswith('m'):
        minutes = int(time_range[:-1])
        start_time = now - timedelta(minutes=minutes)
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)
        return {
            "range": {
                time_field: {
                    "gte": start_timestamp,
                    "lte": end_timestamp
                }
            }
        }
    elif ' to ' in time_range:
        # 处理绝对时间范围 - 转换为毫秒时间戳
        start_str, end_str = time_range.split(' to ')
        start_timestamp = _parse_datetime_to_timestamp(start_str.strip())
        end_timestamp = _parse_datetime_to_timestamp(end_str.strip())

        if start_timestamp is None or end_timestamp is None:
            logger.error(f"时间格式解析失败: {time_range}")
            return None

        return {
            "range": {
                time_field: {
                    "gte": start_timestamp,
                    "lte": end_timestamp
                }
            }
        }
    else:
        return None


async def query_traffic_logs(
    query: Annotated[str, "查询条件 (支持简单文本查询、字段匹配、复杂条件等)"] = "*",
    time_range: Annotated[str, "时间范围，支持多种格式：相对时间('1h', '24h', '7d', '30m')或绝对时间('2019-07-16 to 2019-07-20', '2019-07-16 05:58:52 to 2019-07-16 06:58:52')。注意：时间区间过滤务必使用该参数，不要在query中使用时间范围过滤。"] = "24h",
    size: Annotated[int, "返回结果数量限制 (默认1000，最大10000)"] = 1000,
    fields: Annotated[Optional[List[str]], "要返回的字段列表 (为空则返回所有字段)"] = None
) -> Dict[str, Any]:
    """
    从OpenSearch查询流量日志数据 - 万能查询接口

    :param query: 查询条件，支持多种格式：
                 - 简单文本: "tcp" 或 "***********"
                 - 字段匹配: "protocol:tcp" 或 "source.ip:***********"
                 - 复杂条件: "protocol:tcp AND source.ip:***********"
                 - 通配符: "*" (查询所有)
    :param time_range: 时间范围，支持相对时间(1h, 24h, 7d)和绝对时间(2019-07-01 to 2019-07-31)
    :param size: 返回结果数量限制 (默认1000，最大10000)
    :param fields: 要返回的字段列表 (为空则返回所有字段)
    :return: 包含查询结果和汇总信息的字典
    """
    if not _opensearch_client.client:
        logger.error("OpenSearch客户端未初始化")
        return {"error": "OpenSearch客户端未初始化", "results": []}

    logger.info(f"🔍 [Tool] 开始查询流量日志: {query[:100]}...")
    
    try:
        # 使用默认索引模式
        target_index = _opensearch_client.index_name

        # 构建时间范围过滤器
        time_filter = _build_time_filter(time_range)

        # 构建查询体
        query_body: Dict[str, Any]
        if query.strip().startswith('{'):
            # JSON格式的Query DSL
            query_body = json.loads(query)
        else:
            # 简单查询字符串
            if time_filter:
                query_body = {
                    "query": {
                        "bool": {
                            "must": [
                                {"query_string": {"query": query}}
                            ],
                            "filter": [time_filter]
                        }
                    }
                }
            else:
                # 如果没有时间过滤器，使用简单查询
                query_body = {
                    "query": {
                        "query_string": {"query": query}
                    }
                }
        
        # 添加字段过滤
        if fields:
            query_body["_source"] = fields
        
        # 添加排序
        query_body["sort"] = [{"firstPacket": {"order": "desc"}}]
        query_body["size"] = min(size, 10000)
        
        # 执行查询
        response = _opensearch_client.client.search(
            index=target_index,
            body=query_body
        )
        
        hits = response['hits']['hits']
        total_hits = response['hits']['total']['value']
        
        # 生成汇总信息
        summary = _generate_summary(hits, total_hits)
        
        # 构建结果
        result = {
            "query": query,
            "time_range": time_range,
            "total_hits": total_hits,
            "returned_hits": len(hits),
            "summary": summary,
            "data": hits,
            "query_time": datetime.now().isoformat()
        }
        
        # 保存结果到文件
        filepath = _save_query_results(result, "traffic_logs")
        result["saved_to"] = filepath

        logger.info(f"✅ [Tool] 查询完成: 总计 {total_hits} 条记录，返回 {len(hits)} 条")
        return result

    except Exception as e:
        error_msg = f"查询失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        return {"error": error_msg, "results": []}


def get_information_collection_tool_functions() -> List[Callable]:
    """
    获取所有信息收集工具函数

    :return: 包含所有工具函数的列表
    """
    return [
        query_traffic_logs
    ]
