"""
记忆管理工具集 - 提供统一的记忆管理接口
"""
import json

from typing import Dict, List, Optional, Any
from datetime import datetime

from src.extensions.memory.session_manager import SessionManager
from src.extensions.memory.memory_processor import MemoryProcessor
from src.utils.logger import get_logger

logger = get_logger("memory_tools")

class MemoryTools:
    """记忆管理工具集"""
    
    def __init__(self):
        self.session_manager = SessionManager()
        self.memory_processor = MemoryProcessor()
    
    def create_session(self, description: str, metadata: Optional[Dict[str, Any]] = None,
                      primary_agent: Optional[str] = None, task_type: Optional[str] = None,
                      task_list: Optional[List[Dict[str, Any]]] = None,
                      analysis_plan: Optional[str] = None, user_request: Optional[str] = None) -> Dict[str, Any]:
        """
        工具1: 会话创建

        创建一个新的会话session，维护该会话的记忆空间

        Args:
            description: 会话描述
            metadata: 会话元数据
            primary_agent: 主要Agent名称
            task_type: 任务类型 (main_task, sub_task)
            task_list: 任务清单
            analysis_plan: 分析计划
            user_request: 用户原始请求

        Returns:
            Dict: 包含session_id和会话信息的字典
        """
        try:
            session_id = self.session_manager.create_session(
                description, metadata, primary_agent, task_type,
                task_list, analysis_plan, user_request
            )
            
            # 获取会话信息
            session_info = self.session_manager.get_session_info(session_id)
            
            return {
                "success": True,
                "session_id": session_id,
                "session_info": session_info,
                "message": f"成功创建会话: {session_id}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"创建会话失败: {e}"
            }
    
    def save_conversation(self, session_id: str, messages: List[Dict[str, Any]],
                         auto_summary: bool = True) -> Dict[str, Any]:
        """
        工具2: 会话保存

        根据session_id，将新的对话信息保存到当前session的会话记录中，
        并使用LLM生成消息摘要，更新当前会话的整体总结

        Args:
            session_id: 会话ID
            messages: 对话消息列表
            auto_summary: 是否自动生成摘要

        Returns:
            Dict: 保存结果
        """
        try:
            # 为每条消息生成摘要（如果需要且没有提供）
            enhanced_messages = []
            for msg in messages:
                enhanced_msg = msg.copy()

                # 如果启用自动摘要且消息没有摘要，则生成摘要
                if auto_summary and not enhanced_msg.get('message_summary'):
                    content = enhanced_msg.get('content', '')
                    if content:
                        summary = self.memory_processor.generate_summary(
                            content,
                            "请为以下消息生成简洁摘要："
                        )
                        enhanced_msg['message_summary'] = summary

                enhanced_messages.append(enhanced_msg)

            # 保存对话
            success = self.session_manager.save_conversation(session_id, enhanced_messages)

            if not success:
                return {
                    "success": False,
                    "message": f"保存对话失败 - session_id: {session_id}, messages: {len(enhanced_messages)}"
                }

            # 获取更新后的会话信息
            session_info = self.session_manager.get_session_info(session_id)

            # 如果消息数达到阈值，更新整体总结
            message_count = session_info.get('message_count', 0)
            if message_count > 0 and message_count % 10 == 0:  # 每10条消息更新一次总结
                self._update_session_summary(session_id)

            # 将消息内容和摘要都添加到mem0记忆库
            for i, msg in enumerate(enhanced_messages):
                # 添加原始消息内容
                content = msg.get('content', '')
                if content:
                    self.memory_processor.add_to_mem0(
                        session_id,
                        content,
                        metadata={
                            "type": "message_content",
                            "role": msg.get('role', ''),
                            "message_index": message_count - len(enhanced_messages) + i,
                            "timestamp": msg.get('timestamp', datetime.now().isoformat())
                        }
                    )

                # 添加消息摘要（如果有）
                summary = msg.get('message_summary')
                if summary:
                    self.memory_processor.add_to_mem0(
                        session_id,
                        f"摘要: {summary}",
                        metadata={
                            "type": "message_summary",
                            "role": msg.get('role', ''),
                            "message_index": message_count - len(enhanced_messages) + i,
                            "timestamp": msg.get('timestamp', datetime.now().isoformat())
                        }
                    )

            return {
                "success": True,
                "session_id": session_id,
                "saved_messages": len(enhanced_messages),
                "session_info": session_info,
                "message": f"成功保存 {len(enhanced_messages)} 条消息到会话 {session_id}"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"保存对话失败: {e}"
            }
    
    def query_messages(self, session_id: str, start_index: int = 0,
                      end_index: Optional[int] = None) -> Dict[str, Any]:
        """
        工具3: 会话查询

        查询当前session中原始对话记录列表中的消息内容
        支持单个消息查询和范围查询

        Args:
            session_id: 会话ID
            start_index: 起始索引
            end_index: 结束索引，None表示到最后，如果等于start_index+1则查询单个消息

        Returns:
            Dict: 查询结果
        """
        try:
            # 如果end_index未指定且start_index >= 0，默认查询单个消息
            if end_index is None and start_index >= 0:
                end_index = start_index + 1

            messages = self.session_manager.get_messages(session_id, start_index, end_index)

            if not messages:
                return {
                    "success": False,
                    "message": f"未找到会话 {session_id} 中索引 {start_index} 到 {end_index} 的消息"
                }

            # 如果只查询一个消息，返回单个消息格式
            if len(messages) == 1:
                return {
                    "success": True,
                    "session_id": session_id,
                    "message_index": start_index,
                    "message": messages[0].to_dict(),
                    "result_message": f"成功查询到消息 {session_id}[{start_index}]"
                }
            else:
                return {
                    "success": True,
                    "session_id": session_id,
                    "start_index": start_index,
                    "end_index": end_index,
                    "message_count": len(messages),
                    "messages": [msg.to_dict() for msg in messages],
                    "result_message": f"成功查询到 {len(messages)} 条消息"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"查询消息失败: {e}"
            }
    
    def retrieve_memories(self, query: str, session_id: Optional[str] = None, 
                         global_search: bool = True, limit: int = 10) -> Dict[str, Any]:
        """
        工具4: 记忆检索
        
        给出需要查询的记忆的query，根据session_id检索相关记忆，
        或者在全局范围内检索相关记忆
        
        Args:
            query: 查询内容
            session_id: 会话ID，如果指定则优先搜索该会话的记忆
            global_search: 是否进行全局搜索
            limit: 返回结果数量限制
            
        Returns:
            Dict: 检索结果
        """
        try:
            # 使用记忆处理器搜索
            memories = self.memory_processor.search_memories(
                query, session_id, global_search, limit
            )
            
            # 格式化结果
            formatted_memories = []
            for memory in memories:
                formatted_memory = {
                    "content": memory.get('memory', ''),
                    "session_id": memory.get('session_id', memory.get('user_id', '')),
                    "relevance_score": memory.get('relevance_score', 0.0),
                    "metadata": memory.get('metadata', {}),
                    "timestamp": memory.get('timestamp', memory.get('created_at', ''))
                }
                formatted_memories.append(formatted_memory)
            
            return {
                "success": True,
                "query": query,
                "session_id": session_id,
                "global_search": global_search,
                "total_results": len(formatted_memories),
                "memories": formatted_memories,
                "message": f"检索到 {len(formatted_memories)} 条相关记忆"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"记忆检索失败: {e}"
            }
    
    def list_sessions(self, limit: int = 20) -> Dict[str, Any]:
        """
        辅助工具: 列出所有会话
        
        Args:
            limit: 返回数量限制
            
        Returns:
            Dict: 会话列表
        """
        try:
            sessions = self.session_manager.list_sessions(limit)
            
            return {
                "success": True,
                "total_sessions": len(sessions),
                "sessions": sessions,
                "message": f"找到 {len(sessions)} 个会话"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"获取会话列表失败: {e}"
            }
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """
        辅助工具: 获取会话详细信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict: 会话信息
        """
        try:
            session_info = self.session_manager.get_session_info(session_id)
            
            if session_info is None:
                return {
                    "success": False,
                    "message": f"会话 {session_id} 不存在"
                }
            
            return {
                "success": True,
                "session_info": session_info,
                "message": f"成功获取会话信息: {session_id}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"获取会话信息失败: {e}"
            }
    
    
    def store_analysis_case(self, user_request: str, analysis_plan: str,
                           execution_results: Dict[str, Any], final_report: str) -> str:
        """
        工具5: 存储分析案例

        将完整的分析过程作为案例存储到长期记忆中

        Args:
            user_request: 用户的原始请求
            analysis_plan: 分析计划内容
            execution_results: 执行结果字典
            final_report: 最终分析报告

        Returns:
            str: 创建的会话ID
        """
        try:
            # 创建分析案例会话
            case_description = f"分析案例: {user_request[:100]}..."
            case_metadata = {
                "type": "analysis_case",
                "user_request": user_request,
                "timestamp": datetime.now().isoformat()
            }

            # 从execution_results中提取任务信息
            task_list = []
            executed_subtasks = []
            primary_agent = "ThreatAnalysisAgent"  # 默认主要Agent

            if isinstance(execution_results, dict):
                # 提取任务清单
                if 'task_list' in execution_results:
                    task_list = execution_results['task_list']
                elif 'tasks' in execution_results:
                    task_list = execution_results['tasks']

                # 提取执行的子任务
                if 'execution_results' in execution_results:
                    for result in execution_results['execution_results']:
                        if isinstance(result, dict):
                            executed_subtasks.append({
                                "task_id": result.get('task_id', ''),
                                "task_title": result.get('task', ''),
                                "status": result.get('status', ''),
                                "result": result.get('result', ''),
                                "summary": result.get('summary', ''),
                                "timestamp": datetime.now().isoformat()
                            })

                # 提取主要Agent信息
                if 'primary_agent' in execution_results:
                    primary_agent = execution_results['primary_agent']

            result = self.create_session(
                description=case_description,
                metadata=case_metadata,
                primary_agent=primary_agent,
                task_type="main_task",
                task_list=task_list,
                analysis_plan=analysis_plan,
                user_request=user_request
            )
            if not result['success']:
                raise Exception(f"创建分析案例会话失败: {result.get('error')}")

            session_id = result['session_id']

            # 构建分析案例消息
            messages = [
                {
                    "role": "user",
                    "content": user_request,
                    "timestamp": datetime.now().isoformat(),
                    "message_summary": "用户原始请求",
                    "agent_name": "User",
                    "agent_role": "user",
                    "task_type": "main_task",
                    "task_id": "initial_request",
                    "task_title": "用户需求提交"
                },
                {
                    "role": "assistant",
                    "content": f"分析计划:\n{analysis_plan}",
                    "timestamp": datetime.now().isoformat(),
                    "message_summary": "制定的分析计划",
                    "agent_name": "PlanningAgent",
                    "agent_role": "planning",
                    "task_type": "main_task",
                    "task_id": "planning_phase",
                    "task_title": "分析计划制定"
                },
                {
                    "role": "system",
                    "content": f"执行结果:\n{json.dumps(execution_results, ensure_ascii=False, indent=2)}",
                    "timestamp": datetime.now().isoformat(),
                    "message_summary": "任务执行结果",
                    "agent_name": "SystemExecutor",
                    "agent_role": "system",
                    "task_type": "main_task",
                    "task_id": "execution_phase",
                    "task_title": "任务执行阶段"
                },
                {
                    "role": "assistant",
                    "content": f"最终报告:\n{final_report}",
                    "timestamp": datetime.now().isoformat(),
                    "message_summary": "最终分析报告",
                    "agent_name": primary_agent,
                    "agent_role": "threat_analysis",
                    "task_type": "main_task",
                    "task_id": "final_report",
                    "task_title": "最终报告生成"
                }
            ]

            # 保存分析案例
            save_result = self.save_conversation(session_id, messages, auto_summary=True)
            if not save_result['success']:
                raise Exception(f"保存分析案例失败: {save_result.get('error')}")

            # 更新会话的子任务执行信息
            if executed_subtasks:
                for subtask in executed_subtasks:
                    self.session_manager.add_executed_subtask(session_id, subtask)

            # 添加案例总结到mem0
            case_summary = f"分析案例总结 - 请求: {user_request[:200]}... 报告: {final_report[:200]}..."
            self.memory_processor.add_to_mem0(
                session_id,
                case_summary,
                metadata={
                    "type": "analysis_case_summary",
                    "timestamp": datetime.now().isoformat(),
                    "primary_agent": primary_agent,
                    "task_type": "main_task",
                    "subtask_count": len(executed_subtasks)
                }
            )

            return session_id

        except Exception as e:
            raise Exception(f"存储分析案例失败: {e}")

    def _update_session_summary(self, session_id: str) -> bool:
        """
        内部方法: 更新会话整体总结

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否更新成功
        """
        try:
            # 获取会话数据
            session_info = self.session_manager.get_session_info(session_id)
            if not session_info:
                return False

            # 加载完整会话数据
            session = self.session_manager._load_session(session_id)
            if not session:
                return False

            # 生成整体总结
            overall_summary = self.memory_processor.generate_session_summary(session.to_dict())

            # 更新会话总结
            success = self.session_manager.update_session_summary(session_id, overall_summary)

            # 将总结添加到mem0
            if success and overall_summary:
                self.memory_processor.add_to_mem0(
                    session_id,
                    f"会话整体总结: {overall_summary}",
                    metadata={
                        "type": "session_summary",
                        "timestamp": datetime.now().isoformat()
                    }
                )

            return success

        except Exception as e:
            logger.error(f"❌ 更新会话总结失败: {e}")
            # print(f"❌ 更新会话总结失败: {e}")
            return False
