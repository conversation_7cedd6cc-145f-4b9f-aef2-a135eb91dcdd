# 向量数据库配置
# 支持的类型: qdrant, chroma, faiss, pinecone, weaviate
VECTOR_STORE_TYPE=qdrant

# Embedding模型维度（根据使用的embedding模型设置）
EMBEDDING_DIMS=768

# LLM配置
LLM_BASE_URL=http://***********:18089/v1
LLM_API_KEY=sk-BP_3dbwmFuHI83DcJ-N5XA
LLM_MODEL=secllm-v3

# Embedding配置
EMBEDDING_MODEL=bce-embedding-base-v1
EMBEDDING_BASE_URL=http://*************:5000/api/v1/ml/similarity
EMBEDDING_API_KEY=key-placeholder

# Pinecone配置（如果使用Pinecone）
# PINECONE_API_KEY=your-pinecone-api-key
# PINECONE_ENVIRONMENT=us-west1-gcp
# PINECONE_INDEX_NAME=memory-index

# Weaviate配置（如果使用Weaviate）
# WEAVIATE_URL=http://localhost:8080
# WEAVIATE_API_KEY=your-weaviate-api-key

# Telemetry禁用（可选，如果遇到telemetry相关警告可以设置）
# MEM0_TELEMETRY=false
# POSTHOG_DISABLED=1
