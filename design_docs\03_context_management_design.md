# 🧠 上下文管理架构设计

## 概述

系统采用**混合记忆架构**，结合本地JSON存储和Mem0向量记忆，实现了高效的上下文管理和智能记忆检索。已移除传统的MemoryAgent，改用记忆工具集成到各个Agent中，提供更简化和高效的记忆管理能力。

## 🏗️ 混合记忆管理系统

### 双层存储架构

#### 本地JSON存储层
- **存储内容**: 原始对话记录的完整保存
- **存储位置**: `./memory_storage/sessions/`
- **数据格式**: JSON文件，支持精确的历史回溯
- **优势**:
  - 完整保留原始对话内容
  - 支持离线访问和快速查询
  - 数据结构清晰，易于调试和维护

#### Mem0向量记忆层
- **存储内容**: 智能记忆摘要和语义向量
- **存储位置**: `./memory_storage/mem0_data/chroma_db/`
- **向量数据库**: ChromaDB (本地部署)
- **优势**:
  - 语义相似度检索
  - 跨会话的知识关联
  - 智能记忆摘要和压缩

### 记忆数据结构（实际实现）

#### 会话数据结构
```python
@dataclass
class Session:
    session_id: str                    # 会话唯一标识
    description: str                   # 会话描述
    created_at: str                   # 创建时间
    updated_at: str                   # 更新时间
    messages: List[ConversationMessage] # 对话消息列表
    overall_summary: Optional[str]     # 会话整体总结
    metadata: Dict[str, Any]          # 会话元数据
```

#### 对话消息结构
```python
@dataclass
class ConversationMessage:
    role: str                         # 消息角色 (user/assistant/system)
    content: str                      # 消息内容
    timestamp: str                    # 时间戳
    reasoning: Optional[str]          # 推理过程 (reasoning模型)
    tool_call: Optional[Dict]         # 工具调用信息
    tool_response: Optional[Dict]     # 工具响应信息
    message_summary: Optional[str]    # 消息摘要
    metadata: Optional[Dict]          # 消息元数据
```

### 记忆工具集（实际实现）

#### 核心记忆工具函数
```python
# Agent专用会话管理
async def create_agent_memory_session(agent_id: str, description: str) -> Dict[str, Any]
async def save_agent_interaction(agent_id: str, messages: List[Dict]) -> Dict[str, Any]

# 记忆检索和上下文管理
async def search_agent_memories(agent_id: str, query: str) -> Dict[str, Any]
async def get_agent_context(agent_id: str, current_query: str) -> Dict[str, Any]

# 分析案例存储
async def store_analysis_case(case_title: str, case_content: str) -> Dict[str, Any]
async def search_global_memories(query: str) -> Dict[str, Any]
```

### 记忆操作流程（实际实现）

#### 存储操作
1. **会话创建**: `SessionManager.create_session()` 创建新的Agent会话
2. **消息保存**: `SessionManager.save_conversation()` 保存对话到本地JSON
3. **摘要生成**: `MemoryProcessor.generate_message_summary()` 生成消息摘要
4. **向量存储**: `MemoryProcessor.add_to_mem0()` 添加到Mem0向量记忆

#### 检索操作
1. **本地查询**: `SessionManager.query_messages()` 查询本地对话记录
2. **语义检索**: `MemoryProcessor.search_memories()` 进行向量相似度检索
3. **上下文构建**: `MemoryIntegrationAdapter.format_context_for_agent()` 格式化Agent上下文
4. **结果合并**: 合并本地和向量检索结果

#### 维护操作
1. **会话总结**: `MemoryProcessor.generate_session_summary()` 生成会话总结
2. **自动摘要**: 每10轮对话自动生成摘要
3. **数据清理**: 定期清理过期会话数据
4. **向量优化**: Mem0自动优化向量索引

## 🔄 Agent上下文管理机制

### Agent专用会话系统

#### 会话隔离策略
- **独立会话空间**: 每个Agent拥有独立的记忆会话
- **会话ID映射**: `MemoryIntegrationAdapter._active_sessions` 维护Agent到会话的映射
- **自动会话创建**: Agent首次使用记忆工具时自动创建专用会话
- **会话元数据**: 记录Agent类型、创建时间等信息

#### 上下文构建策略
```python
def format_context_for_agent(self, agent_id: str, query: str,
                            max_context_length: int = 2000) -> str:
    """为Agent构建个性化上下文"""
    # 1. 获取Agent会话历史
    recent_messages = self.list_agent_messages(agent_id, limit=5)

    # 2. 检索相关记忆
    relevant_memories = self.retrieve_agent_memories(agent_id, query, limit=3)

    # 3. 构建格式化上下文
    context_parts = []

    # 添加最近对话历史
    if recent_messages:
        context_parts.append("## 最近对话历史")
        for msg in recent_messages[-3:]:  # 最近3条
            context_parts.append(f"- {msg['role']}: {msg['content'][:100]}...")

    # 添加相关记忆
    if relevant_memories:
        context_parts.append("## 相关记忆")
        for memory in relevant_memories:
            context_parts.append(f"- {memory['content'][:150]}...")

    # 控制总长度
    full_context = "\n".join(context_parts)
    if len(full_context) > max_context_length:
        full_context = full_context[:max_context_length] + "..."

    return full_context
```

### 智能上下文筛选

#### 相关性计算（基于Mem0）
- **语义相似度**: 使用Mem0的向量相似度计算
- **时间权重**: 最近的记忆获得更高权重
- **频率权重**: 经常访问的记忆优先级更高
- **Agent特异性**: 优先返回Agent专用的记忆

#### 长度控制策略
- **默认上下文长度**: 2000字符
- **智能截断**: 保留重要信息，截断次要内容
- **分层压缩**: 优先保留高优先级内容
- **动态调整**: 根据Agent类型和任务复杂度调整长度

### 上下文压缩算法（实际实现）

#### 基于Mem0的智能摘要
```python
def generate_message_summary(self, message_content: str) -> str:
    """使用LLM生成消息摘要"""
    if len(message_content) <= 100:
        return message_content

    prompt = f"""请为以下消息生成简洁的摘要（不超过50字）：

    消息内容：
    {message_content}

    摘要："""

    # 调用LLM生成摘要
    response = self.llm_client.chat.completions.create(
        model=self.config.llm_config["config"]["model"],
        messages=[{"role": "user", "content": prompt}],
        max_tokens=100,
        temperature=0.1
    )

    return response.choices[0].message.content.strip()
```

#### 会话级摘要生成
```python
def generate_session_summary(self, session_data: Dict[str, Any]) -> str:
    """生成会话整体总结"""
    messages = session_data.get('messages', [])
    if not messages:
        return "空会话"

    # 提取关键信息
    key_points = []
    for msg in messages:
        if msg.get('message_summary'):
            key_points.append(msg['message_summary'])
        elif len(msg.get('content', '')) > 50:
            key_points.append(msg['content'][:50] + "...")

    # 生成整体摘要
    summary_content = " | ".join(key_points[:5])  # 最多5个要点
    return f"会话摘要: {summary_content}"
```

## 🤖 Agent上下文管理（实际实现）

### Agent记忆工具集成

#### 统一记忆接口
所有Agent都配置了相同的记忆管理工具：
```python
"tools": [
    "create_agent_memory_session",  # 创建Agent专用会话
    "save_agent_interaction",       # 保存Agent交互记录
    "search_agent_memories",        # 搜索Agent相关记忆
    "get_agent_context"            # 获取Agent格式化上下文
]
```

#### Agent特定上下文需求
- **PlanningAgent**: 需要历史分析案例、分析模板和最佳实践
- **ReflectionAgent**: 需要任务执行历史、质量评估标准和决策记录
- **ThreatAnalysisAgent**: 需要技术知识、威胁情报和分析方法
- **ToolExecutionAgent**: 需要工具使用历史、执行结果和错误处理经验
- **ReportAgent**: 需要完整的分析过程、报告模板和案例存储

### 上下文同步机制（实际实现）

#### 任务级上下文同步
- **MainTaskManager**: 维护全局分析计划和执行状态
- **SubTaskManager**: 管理子任务的工具执行和分析结论
- **记忆工具同步**: 各Agent通过记忆工具自动同步上下文

#### EnhancedGroupChatManager上下文管理
```python
class GroupChatContext:
    task_info: Dict[str, Any]                    # 任务信息
    global_conversation: List[AgentExecutionRecord]  # 全局对话历史
    agent_contexts: Dict[str, List[Dict]]        # Agent专用上下文
    current_round: int                           # 当前轮次
    max_rounds: int                             # 最大轮次
```

## 🔄 上下文流转流程（实际实现）

### 实际流转路径

#### 6步流程中的上下文流转
```
用户请求 → 记忆检索(工具化) → PlanningAgent(计划+记忆工具) →
主任务执行 → ReflectionAgent(任务确定+记忆工具) →
子任务协作(EnhancedGroupChatManager) → ReportAgent(报告+记忆工具) →
知识存储(工具化)
```

#### 子任务协作中的上下文流转
```
ThreatAnalysisAgent(分析+记忆工具) ↔ ToolExecutionAgent(执行+记忆工具)
                    ↓
            EnhancedGroupChatManager(智能选择+上下文管理)
                    ↓
            Agent专用上下文更新 + 全局对话历史记录
```

### 流转控制机制

#### 记忆工具控制
- **会话隔离**: 每个Agent独立的记忆会话空间
- **自动上下文构建**: 根据当前查询自动构建相关上下文
- **长度控制**: 智能控制上下文长度，避免信息过载
- **相关性筛选**: 基于Mem0的语义相似度筛选相关记忆

#### 安全和隐私控制
- **敏感信息处理**: 在记忆存储时自动识别和处理敏感信息
- **访问权限**: Agent只能访问自己的专用会话和全局记忆
- **审计日志**: 完整记录记忆访问和操作历史

## ⚡ 性能优化（实际实现）

### 缓存策略
- **会话缓存**: `SessionManager._sessions_cache` 缓存活跃会话
- **Agent会话映射缓存**: `MemoryIntegrationAdapter._active_sessions` 缓存映射关系
- **Mem0向量缓存**: ChromaDB自动缓存向量检索结果

### 存储优化
- **增量存储**: 只存储新增的对话消息，避免重复写入
- **分页查询**: 支持分页查询历史消息，减少内存占用
- **自动清理**: 定期清理过期会话和低价值记忆

### 检索优化
- **混合检索**: 结合本地JSON查询和Mem0向量检索
- **相关性排序**: 基于时间、频率和语义相似度的综合排序
- **结果限制**: 智能限制检索结果数量，平衡相关性和性能

### 内存管理
- **会话数据分页**: 大量历史消息的分页加载
- **按需加载**: 只在需要时加载完整会话数据
- **自动清理**: 定期清理过期会话和缓存数据

## 🔒 质量保证（实际实现）

### 数据一致性
- **会话数据一致性**: 确保JSON存储和Mem0存储的数据一致
- **Agent会话映射一致性**: 维护Agent到会话ID的准确映射
- **时间戳一致性**: 所有记录使用统一的时间戳格式

### 错误处理
- **存储失败处理**: 本地存储失败时的降级策略
- **Mem0连接失败**: 向量记忆不可用时的备用方案
- **数据损坏恢复**: 检测和修复损坏的会话数据

### 监控和诊断
- **记忆使用统计**: 跟踪记忆存储和检索的使用情况
- **性能监控**: 监控记忆操作的响应时间和成功率
- **容量管理**: 监控存储空间使用和自动清理策略

## 📈 扩展性设计

### 存储后端扩展
- **可插拔存储**: 支持扩展新的存储后端（数据库、云存储等）
- **存储适配器**: 统一的存储接口，便于切换存储方案
- **数据迁移**: 支持不同存储后端间的数据迁移

### 记忆类型扩展
- **自定义记忆类型**: 支持定义新的记忆分类和处理逻辑
- **记忆处理器扩展**: 可插拔的记忆处理和摘要算法
- **检索策略扩展**: 支持自定义的记忆检索和排序策略

### Agent集成扩展
- **新Agent类型**: 新Agent可以无缝集成记忆管理工具
- **自定义上下文**: 支持Agent特定的上下文构建逻辑
- **记忆权限**: 灵活的记忆访问权限控制机制

### 准确性保障
- **信息验证**: 验证记忆内容的准确性
- **来源追踪**: 记录信息的来源和变更历史
- **版本控制**: 支持记忆内容的版本管理
