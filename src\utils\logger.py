"""
简单的日志系统
使用Python标准logging模块，同时输出到控制台和文件
"""

import logging
import os
from pathlib import Path
from datetime import datetime


def setup_logger(name: str = "traffic_analysis", level: str | None = None) -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR)，如果为None则从配置文件读取

    Returns:
        配置好的日志记录器
    """
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)

    # 如果已经配置过，直接返回
    if logger.handlers:
        return logger

    # 从配置文件读取日志配置
    try:
        from src.config import get_config
        log_config = get_config("app", "logging", {})

        # 设置日志级别
        if level:
            log_level = level.upper()
        else:
            log_level = log_config.get("level", "INFO").upper()

        console_level = log_config.get("console_level", "INFO").upper()
        file_level = log_config.get("file_level", "DEBUG").upper()
        log_format = log_config.get("format", "%(asctime)s - %(levelname)s - %(name)s - %(message)s")
        date_format = log_config.get("date_format", "%m/%d/%Y %H:%M:%S")

    except Exception:
        # 如果配置读取失败，使用默认值
        log_level = level.upper() if level else "INFO"
        console_level = "INFO"
        file_level = "DEBUG"
        log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
        date_format = "%m/%d/%Y %H:%M:%S"

    logger.setLevel(getattr(logging, log_level, logging.INFO))

    # 创建格式化器
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, console_level, logging.INFO))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器 - 按日期命名
    today = datetime.now().strftime("%Y%m%d")
    log_file = log_dir / f"traffic_analysis_{today}.log"

    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, file_level, logging.DEBUG))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger


# 创建全局日志记录器
logger = setup_logger()


def get_logger(name: str | None = None) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称，如果为None则返回全局记录器

    Returns:
        日志记录器
    """
    if name:
        return setup_logger(name)
    return logger


def print_llm_interaction(agent_name: str, model_name: str, interaction_type: str, content: str, **kwargs):
    """
    简单直接打印LLM交互信息

    Args:
        agent_name: Agent名称
        model_name: 模型名称
        interaction_type: 交互类型 (REQUEST/RESPONSE/REASONING/TOOL_CALL/ERROR)
        content: 主要内容
        **kwargs: 其他信息
    """
    timestamp = datetime.now().strftime("%H:%M:%S")

    # 基础信息行
    # print(f"\n🤖 [{timestamp}] {interaction_type} | {agent_name} | {model_name}")
    # print("=" * 80)
    logger.info(f"🤖 [{timestamp}] {interaction_type} | {agent_name} | {model_name}")
    logger.info("=" * 80)

    # 内容预览（限制长度避免过长输出）
    # if len(content) > 500:
    #     content_preview = content[:500] + "\n... [内容过长，已截断] ..."
    # else:
    #     content_preview = content
        
    content_preview = content

    # print(content_preview)
    logger.info(content_preview)

    # 额外信息
    if kwargs:
        # print("\n--- 额外信息 ---")
        logger.info("\n--- 额外信息 ---")
        for key, value in kwargs.items():
            if isinstance(value, (list, dict)):
                # print(f"{key}: {len(value) if isinstance(value, list) else len(value.keys())} 项")
                logger.info(f"{key}: {len(value) if isinstance(value, list) else len(value.keys())} 项")
            else:
                # print(f"{key}: {value}")
                logger.info(f"{key}: {value}")

    # print("=" * 80)
    logger.info("=" * 80)
