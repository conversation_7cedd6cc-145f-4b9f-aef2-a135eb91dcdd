"""
用户交互接口抽象层

提供统一的用户交互和展示接口，支持：
1. 命令行界面 (CLI)
2. Web界面 (预留)
3. GUI界面 (预留)
4. API接口 (预留)
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime


class UIType(Enum):
    """用户界面类型"""
    CLI = "cli"
    WEB = "web"
    GUI = "gui"
    API = "api"


class MessageType(Enum):
    """消息类型"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    QUESTION = "question"
    PROGRESS = "progress"


class InputType(Enum):
    """输入类型"""
    TEXT = "text"
    CHOICE = "choice"
    CONFIRM = "confirm"
    FILE = "file"
    PASSWORD = "password"


@dataclass
class UIMessage:
    """UI消息数据结构"""
    message_type: MessageType
    content: str
    title: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UIInput:
    """UI输入请求数据结构"""
    input_type: InputType
    prompt: str
    default_value: Optional[str] = None
    choices: Optional[List[str]] = None
    required: bool = True
    validation_func: Optional[Callable[[str], bool]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProgressInfo:
    """进度信息"""
    current: int
    total: int
    message: str
    percentage: Optional[float] = None
    
    def __post_init__(self):
        if self.percentage is None:
            self.percentage = (self.current / self.total * 100) if self.total > 0 else 0


class BaseUserInterface(ABC):
    """用户界面基类"""
    
    def __init__(self, ui_type: UIType):
        self.ui_type = ui_type
        self.is_initialized = False
        self.message_history: List[UIMessage] = []
        self.current_session_id: Optional[str] = None
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化界面"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        pass
    
    @abstractmethod
    async def display_message(self, message: UIMessage):
        """显示消息"""
        pass
    
    @abstractmethod
    async def get_user_input(self, input_request: UIInput) -> Optional[str]:
        """获取用户输入"""
        pass
    
    @abstractmethod
    async def show_progress(self, progress: ProgressInfo):
        """显示进度"""
        pass
    
    @abstractmethod
    async def display_analysis_result(self, result: Dict[str, Any]):
        """显示分析结果"""
        pass
    
    @abstractmethod
    async def display_task_status(self, task_status: Dict[str, Any]):
        """显示任务状态"""
        pass
    
    def add_message_to_history(self, message: UIMessage):
        """添加消息到历史记录"""
        self.message_history.append(message)
        
        # 保持历史记录在合理范围内
        if len(self.message_history) > 1000:
            self.message_history = self.message_history[-500:]
    
    async def info(self, content: str, title: Optional[str] = None):
        """显示信息消息"""
        message = UIMessage(MessageType.INFO, content, title)
        self.add_message_to_history(message)
        await self.display_message(message)
    
    async def success(self, content: str, title: Optional[str] = None):
        """显示成功消息"""
        message = UIMessage(MessageType.SUCCESS, content, title)
        self.add_message_to_history(message)
        await self.display_message(message)
    
    async def warning(self, content: str, title: Optional[str] = None):
        """显示警告消息"""
        message = UIMessage(MessageType.WARNING, content, title)
        self.add_message_to_history(message)
        await self.display_message(message)
    
    async def error(self, content: str, title: Optional[str] = None):
        """显示错误消息"""
        message = UIMessage(MessageType.ERROR, content, title)
        self.add_message_to_history(message)
        await self.display_message(message)
    
    async def ask_text(self, prompt: str, default: Optional[str] = None, 
                      required: bool = True) -> Optional[str]:
        """询问文本输入"""
        input_request = UIInput(InputType.TEXT, prompt, default, required=required)
        return await self.get_user_input(input_request)
    
    async def ask_choice(self, prompt: str, choices: List[str], 
                        default: Optional[str] = None) -> Optional[str]:
        """询问选择"""
        input_request = UIInput(InputType.CHOICE, prompt, default, choices)
        return await self.get_user_input(input_request)
    
    async def ask_confirm(self, prompt: str, default: bool = False) -> bool:
        """询问确认"""
        default_str = "y" if default else "n"
        input_request = UIInput(InputType.CONFIRM, prompt, default_str)
        result = await self.get_user_input(input_request)
        return bool(result and result.lower() in ['y', 'yes', 'true', '1'])


class CLIUserInterface(BaseUserInterface):
    """命令行用户界面实现"""
    
    def __init__(self):
        super().__init__(UIType.CLI)
        self.colors = {
            MessageType.INFO: "\033[94m",      # 蓝色
            MessageType.SUCCESS: "\033[92m",   # 绿色
            MessageType.WARNING: "\033[93m",   # 黄色
            MessageType.ERROR: "\033[91m",     # 红色
            MessageType.QUESTION: "\033[96m",  # 青色
        }
        self.reset_color = "\033[0m"
        self.icons = {
            MessageType.INFO: "ℹ️",
            MessageType.SUCCESS: "✅",
            MessageType.WARNING: "⚠️",
            MessageType.ERROR: "❌",
            MessageType.QUESTION: "❓",
            MessageType.PROGRESS: "🔄"
        }
    
    async def initialize(self) -> bool:
        """初始化CLI界面"""
        try:
            print("🚀 初始化命令行界面...")
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"❌ CLI初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理CLI资源"""
        print("👋 CLI界面已关闭")
    
    async def display_message(self, message: UIMessage):
        """显示消息到CLI"""
        color = self.colors.get(message.message_type, "")
        icon = self.icons.get(message.message_type, "")
        reset = self.reset_color
        
        timestamp = message.timestamp.strftime("%H:%M:%S")
        
        if message.title:
            print(f"{color}{icon} [{timestamp}] {message.title}: {message.content}{reset}")
        else:
            print(f"{color}{icon} [{timestamp}] {message.content}{reset}")
    
    async def get_user_input(self, input_request: UIInput) -> Optional[str]:
        """获取CLI用户输入"""
        try:
            if input_request.input_type == InputType.TEXT:
                prompt = f"❓ {input_request.prompt}"
                if input_request.default_value:
                    prompt += f" (默认: {input_request.default_value})"
                prompt += ": "
                
                user_input = input(prompt).strip()
                if not user_input and input_request.default_value:
                    user_input = input_request.default_value
                
                if input_request.required and not user_input:
                    await self.error("输入不能为空，请重新输入")
                    return await self.get_user_input(input_request)
                
                return user_input
            
            elif input_request.input_type == InputType.CHOICE:
                if not input_request.choices:
                    await self.error("此问题没有可用的选项。")
                    return None
                
                print(f"❓ {input_request.prompt}")
                for i, choice in enumerate(input_request.choices, 1):
                    marker = " (默认)" if choice == input_request.default_value else ""
                    print(f"  {i}. {choice}{marker}")
                
                while True:
                    try:
                        choice_input = input("请选择 (输入数字或选项名): ").strip()
                        
                        if not choice_input and input_request.default_value:
                            return input_request.default_value
                        
                        # 尝试按数字选择
                        if choice_input.isdigit():
                            choice_index = int(choice_input) - 1
                            if 0 <= choice_index < len(input_request.choices):
                                return input_request.choices[choice_index]
                        
                        # 尝试按名称选择
                        for choice in input_request.choices:
                            if choice.lower() == choice_input.lower():
                                return choice
                        
                        print("❌ 无效选择，请重新输入")
                        
                    except (ValueError, IndexError):
                        print("❌ 无效输入，请重新输入")
            
            elif input_request.input_type == InputType.CONFIRM:
                prompt = f"❓ {input_request.prompt} (y/n)"
                if input_request.default_value:
                    default_display = "Y" if input_request.default_value.lower() == "y" else "N"
                    prompt += f" [默认: {default_display}]"
                prompt += ": "
                
                user_input = input(prompt).strip().lower()
                if not user_input and input_request.default_value:
                    user_input = input_request.default_value.lower()
                
                return user_input
            
            return None
            
        except KeyboardInterrupt:
            print("\n❌ 用户取消输入")
            return None
        except Exception as e:
            await self.error(f"输入错误: {e}")
            return None
    
    async def show_progress(self, progress: ProgressInfo):
        """显示CLI进度"""
        bar_length = 30
        filled_length = int(bar_length * progress.current // progress.total)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        print(f"\r🔄 {progress.message} [{bar}] {progress.percentage:.1f}% ({progress.current}/{progress.total})", end="", flush=True)
        
        if progress.current >= progress.total:
            print()  # 换行
    
    async def display_analysis_result(self, result: Dict[str, Any]):
        """显示分析结果"""
        print("\n" + "="*60)
        print("📊 分析结果")
        print("="*60)
        
        if "session_id" in result:
            print(f"会话ID: {result['session_id']}")
        
        if "final_report" in result:
            print("\n📄 最终报告:")
            print("-" * 40)
            print(result['final_report'])
        
        if "task_summary" in result:
            print("\n📋 任务摘要:")
            print("-" * 40)
            summary = result['task_summary']
            print(f"总任务数: {summary.get('total_tasks', 0)}")
            print(f"已完成: {summary.get('completed', 0)}")
            print(f"失败: {summary.get('failed', 0)}")
        
        print("="*60)
    
    async def display_task_status(self, task_status: Dict[str, Any]):
        """显示任务状态"""
        print("\n📋 任务状态")
        print("-" * 40)
        
        if "current_phase" in task_status:
            print(f"当前阶段: {task_status['current_phase']}")
        
        if "progress" in task_status:
            progress = task_status['progress']
            print(f"进度: {progress.get('completed', 0)}/{progress.get('total', 0)}")
        
        if "current_task" in task_status:
            print(f"当前任务: {task_status['current_task']}")
        
        print("-" * 40)


class UIManager:
    """用户界面管理器"""
    
    def __init__(self):
        self.interfaces: Dict[UIType, BaseUserInterface] = {}
        self.current_interface: Optional[BaseUserInterface] = None
        self.default_ui_type = UIType.CLI
    
    def register_interface(self, interface: BaseUserInterface):
        """注册用户界面"""
        self.interfaces[interface.ui_type] = interface
        print(f"🔧 注册用户界面: {interface.ui_type.value}")
    
    async def set_active_interface(self, ui_type: UIType) -> bool:
        """设置活跃界面"""
        if ui_type not in self.interfaces:
            print(f"❌ 未找到界面类型: {ui_type.value}")
            return False
        
        # 清理当前界面
        if self.current_interface:
            await self.current_interface.cleanup()
        
        # 初始化新界面
        interface = self.interfaces[ui_type]
        if await interface.initialize():
            self.current_interface = interface
            print(f"✅ 切换到界面: {ui_type.value}")
            return True
        else:
            print(f"❌ 界面初始化失败: {ui_type.value}")
            return False
    
    async def get_current_interface(self) -> Optional[BaseUserInterface]:
        """获取当前活跃界面"""
        if not self.current_interface:
            # 尝试使用默认界面
            await self.set_active_interface(self.default_ui_type)
        
        return self.current_interface
    
    async def cleanup_all(self):
        """清理所有界面"""
        for interface in self.interfaces.values():
            if interface.is_initialized:
                await interface.cleanup()


# 全局UI管理器实例
ui_manager = UIManager()


def get_ui_manager() -> UIManager:
    """获取全局UI管理器"""
    return ui_manager


async def initialize_default_ui() -> bool:
    """初始化默认用户界面"""
    cli_interface = CLIUserInterface()
    ui_manager.register_interface(cli_interface)
    return await ui_manager.set_active_interface(UIType.CLI)
