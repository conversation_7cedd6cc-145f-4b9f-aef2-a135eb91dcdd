"""
增强版GroupChat管理器

完全自主的Agent交互协调器，支持：
1. 自主Agent选择和消息传递控制
2. 个性化Agent上下文管理
3. 全局对话历史记录和整理
4. LLM驱动的对话总结分析
"""

from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
from typing import cast

from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.messages import TextMessage
from src.utils.logger import get_logger, print_llm_interaction

from src.config import get_config
from src.agent_settings.agent_roles import AgentRole
from src.tools import get_memory_tool_functions, get_all_tool_functions, get_information_collection_tool_functions, get_execution_tool_functions, get_threat_intelligence_tool_functions
from src.core.base_managed_agent import BaseManagedAgent
from src.core.task_manager import MainTaskManager, SubTaskManager, TaskStatus


# === 核心数据结构 ===

@dataclass
class AgentExecutionRecord:
    """Agent执行记录 - 记录每次Agent调用的完整信息"""
    agent_name: str
    agent_role: AgentRole
    input_messages: List[TextMessage]
    context_info: Dict[str, Any]

    # 统一消息类型和内容
    message_type: str = "assistant"  # "user", "assistant", "system", "tool"
    reasoning_content: str = ""  # reasoning模型的思考过程

    # 工具调用详情
    tool_calls: List[Dict[str, Any]] = field(default_factory=list)
    tool_results: List[Dict[str, Any]] = field(default_factory=list)

    # 输出和元数据
    output_message: Optional[TextMessage] = None
    execution_metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    execution_duration: float = 0.0

    def get_formatted_summary(self) -> str:
        """获取格式化的执行摘要"""
        summary_parts = [f"Agent: {self.agent_name} ({self.agent_role.value})"]
        summary_parts.append(f"消息类型: {self.message_type}")

        if self.reasoning_content:
            summary_parts.append(f"推理过程: {self.reasoning_content[:100]}...")

        if self.tool_calls:
            summary_parts.append(f"工具调用: {len(self.tool_calls)} 次")
            for tool_call in self.tool_calls:
                summary_parts.append(f"  - {tool_call.get('name', 'Unknown')}: {tool_call.get('status', 'Unknown')}")

        if self.output_message:
            output_content = getattr(self.output_message, 'content', str(self.output_message))
            summary_parts.append(f"输出: {output_content[:100]}...")

        summary_parts.append(f"执行时长: {self.execution_duration:.2f}秒")

        return "\n".join(summary_parts)

@dataclass
class GroupChatContext:
    """GroupChat全局上下文"""
    session_id: str
    task_info: Dict[str, Any]
    messages: List[AgentExecutionRecord] = field(default_factory=list)  # 统一为messages
    conversation_summary: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


class EnhancedGroupChatManager:
    """
    完全自主的GroupChat管理器

    专门用于子任务执行阶段的Agent协作，具备：
    - 完全自主的Agent选择和消息传递控制
    - 个性化Agent上下文管理
    - 全局对话历史记录和LLM总结
    - 灵活的Agent执行流程控制
    """

    def __init__(self, user_proxy, context_provider):
        self.logger = get_logger("groupchat_manager")
        self.logger.info("🚀 正在初始化自主GroupChatManager...")
        # print("🚀 正在初始化自主GroupChatManager...")

        # 基础属性
        self.user_proxy = user_proxy
        self.context_provider = context_provider

        # 读取配置
        groupchat_config = get_config("groupchat")
        loop_config = get_config("loop_control")
        self.max_rounds = groupchat_config.get("max_rounds", 10)
        self.progress_message_template = loop_config.get("progress_message_template",
            "当前{context}，你最多只能分析{max_count}次，现在已经是第{current}次分析")

        # 初始化Agents
        self.agents: Dict[str, BaseManagedAgent] = {}
        self._initialize_agents()

        # 创建LLM客户端用于Agent选择和对话总结
        self.model_client = self._create_model_client()

        # 全局上下文管理
        self.current_context: Optional[GroupChatContext] = None

        self.logger.info("🚀 自主GroupChatManager初始化完成")
        # print("🚀 自主GroupChatManager初始化完成")

    def _initialize_agents(self):
        """初始化所有Agents"""
        try:
            from src.agent_settings.threat_analysis_agent import AGENT_CONFIG as threat_config
            from src.agent_settings.traffic_log_agent import AGENT_CONFIG as traffic_log_config
            from src.agent_settings.code_execution_agent import AGENT_CONFIG as code_execution_config
            from src.agent_settings.threat_intelligence_agent import AGENT_CONFIG as threat_intelligence_config
            from src.agent_settings.planning_agent import AGENT_CONFIG as planning_config
            from src.agent_settings.reflection_agent import AGENT_CONFIG as reflection_config
            from src.agent_settings.report_agent import AGENT_CONFIG as report_config

            agent_configs = {
                'threat_analysis': (BaseManagedAgent, threat_config, AgentRole.THREAT_ANALYSIS),
                'traffic_log': (BaseManagedAgent, traffic_log_config, AgentRole.TOOL_EXECUTION),  # 使用TOOL_EXECUTION角色，因为它也是执行类Agent
                'code_execution': (BaseManagedAgent, code_execution_config, AgentRole.TOOL_EXECUTION),  # 代码执行Agent，使用TOOL_EXECUTION角色
                'threat_intelligence': (BaseManagedAgent, threat_intelligence_config, AgentRole.TOOL_EXECUTION),  # 威胁情报Agent，使用TOOL_EXECUTION角色
                # 'planning': (BaseManagedAgent, planning_config, AgentRole.PLANNING),
                # 'reflection': (BaseManagedAgent, reflection_config, AgentRole.REFLECTION),
                # 'report': (BaseManagedAgent, report_config, AgentRole.REPORT),
            }

            litellm_config = get_config("litellm")

            for config_name, (agent_class, config, role) in agent_configs.items():
                try:
                    model_name = config.get("model_name")
                    if not model_name:
                        self.logger.warning(f"⚠️ Agent {config_name} 未配置model_name，跳过")
                        # print(f"⚠️ Agent {config_name} 未配置model_name，跳过")
                        continue

                    from autogen_core.models import ModelInfo
                    model_info = ModelInfo(
                        vision=False, function_calling=True, json_output=False,
                        family="unknown", structured_output=False, multiple_system_messages=True,
                    )

                    agent_model_client = OpenAIChatCompletionClient(
                        model=model_name, api_key=litellm_config["api_key"],
                        base_url=litellm_config["base_url"], model_info=model_info
                    )

                    # 根据Agent类型加载相应的工具
                    agent_tools = []
                    if config_name == 'traffic_log':
                        # 流量日志Agent加载信息收集工具和记忆工具
                        
                        info_tools = get_information_collection_tool_functions()
                        memory_tools = []  # get_memory_tool_functions()
                        agent_tools = info_tools + memory_tools

                        if agent_tools:
                            self.logger.info(f"     ✅ 成功为 {config_name} 加载了 {len(info_tools)} 个信息收集工具和 {len(memory_tools)} 个记忆工具。")
                            # print(f"     ✅ 成功为 {config_name} 加载了 {len(info_tools)} 个信息收集工具和 {len(memory_tools)} 个记忆工具。")
                        else:
                            self.logger.warning(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                            # print(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                    elif config_name == 'code_execution':
                        # 代码执行Agent加载执行工具和记忆工具
                        execution_tools = get_execution_tool_functions()
                        memory_tools = []  # get_memory_tool_functions()
                        agent_tools = execution_tools + memory_tools

                        if agent_tools:
                            self.logger.info(f"     ✅ 成功为 {config_name} 加载了 {len(execution_tools)} 个执行工具和 {len(memory_tools)} 个记忆工具。")
                            # print(f"     ✅ 成功为 {config_name} 加载了 {len(execution_tools)} 个执行工具和 {len(memory_tools)} 个记忆工具。")
                        else:
                            self.logger.warning(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                            # print(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                    elif config_name == 'threat_intelligence':
                        # 威胁情报Agent加载威胁情报工具和记忆工具
                        threat_intel_tools = get_threat_intelligence_tool_functions()
                        memory_tools = []  # get_memory_tool_functions()
                        agent_tools = threat_intel_tools + memory_tools

                        if agent_tools:
                            self.logger.info(f"     ✅ 成功为 {config_name} 加载了 {len(threat_intel_tools)} 个威胁情报工具和 {len(memory_tools)} 个记忆工具。")
                            # print(f"     ✅ 成功为 {config_name} 加载了 {len(threat_intel_tools)} 个威胁情报工具和 {len(memory_tools)} 个记忆工具。")
                        else:
                            self.logger.warning(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                            # print(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                    else:
                        # 其他Agent只加载记忆管理工具
                        agent_tools = []  # get_memory_tool_functions()
                        if agent_tools:
                            self.logger.info(f"     ✅ 成功为 {config_name} 加载了 {len(agent_tools)} 个记忆管理工具。")
                            # print(f"     ✅ 成功为 {config_name} 加载了 {len(agent_tools)} 个记忆管理工具。")
                        else:
                            self.logger.warning(f"     ⚠️ 未找到为 {config_name} 加载的工具。")
                            # print(f"     ⚠️ 未找到为 {config_name} 加载的工具。")

                    agent = agent_class(
                        name=config["name"],
                        system_message=config["system_message"],
                        model_client=agent_model_client,
                        context_provider=self.context_provider,
                        agent_role=role,
                        tools=agent_tools
                    )
                    self.logger.info(f"   🤖 创建 {config.get('display_name', config['name'])} (角色: {role.value}) - 使用模型: {model_name}")
                    # print(f"   🤖 创建 {config.get('display_name', config['name'])} (角色: {role.value}) - 使用模型: {model_name}")

                    # 存储到字典中，以agent名称为key
                    self.agents[config["name"]] = agent

                except Exception as e:
                    self.logger.error(f"❌ 创建Agent {config_name} 失败: {e}")
                    # print(f"❌ 创建Agent {config_name} 失败: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"❌ 初始化Agents失败: {e}")
            # print(f"❌ 初始化Agents失败: {e}")
            self.agents = {}

    def _create_model_client(self):
        """创建模型客户端"""
        litellm_config = get_config("litellm")

        from autogen_core.models import ModelInfo

        model_info = ModelInfo(
            vision=False,
            function_calling=True,
            json_output=False,
            family="unknown",
            structured_output=False,
            multiple_system_messages=True,
        )

        return OpenAIChatCompletionClient(
            model=litellm_config["select_model"],
            api_key=litellm_config["api_key"],
            base_url=litellm_config["base_url"],
            model_info=model_info
        )

    def _initialize_context(self, main_task_manager: MainTaskManager,
                           current_task: SubTaskManager) -> GroupChatContext:
        """初始化GroupChat上下文"""
        import uuid

        task_info = {
            "session_id": main_task_manager.session_id,
            "user_request": main_task_manager.user_request,
            "analysis_plan": main_task_manager.analysis_plan,
            "current_task_title": current_task.task_title,
            "current_task_id": current_task.main_task_id
        }

        return GroupChatContext(
            session_id=str(uuid.uuid4()),
            task_info=task_info
        )
    async def _select_next_agent(self, context: GroupChatContext,
                                current_message: str) -> Optional[str]:
        """智能选择下一个Agent"""
        try:
            # 构建选择提示词
            conversation_history = self._format_conversation_history(context)
            available_agents = list(self.agents.keys())

            selector_prompt = f"""你是智能Agent选择器，负责根据当前对话状态选择最合适的下一个Agent。

## 当前任务信息：
- 任务标题：{context.task_info.get('current_task_title', 'N/A')}
- 用户请求：{context.task_info.get('user_request', 'N/A')}

## 可用Agents：
{chr(10).join([f"- {name}: {self.agents[name].agent_role.value}" for name in available_agents])}

## 选择原则：
1. **威胁分析**：需要安全分析、策略制定时选择ThreatAnalysisAgent
2. **代码执行**：需要执行Python代码、Ubuntu命令时选择CodeExecutionAgent
3. **流量分析**：需要查询OpenSearch流量日志时选择TrafficLogAgent
4. **威胁情报**：需要查询IP、域名、URL、文件哈希、CVE漏洞、APT组织等威胁情报时选择ThreatIntelligenceAgent

## 分析流程与Agent协作规则
1. 没有对话历史的子任务，首先由 ThreatAnalysisAgent 负责安全分析、策略制定。
2. 然后由 TrafficLogAgent、ThreatIntelligenceAgent、ThreatAnalysisAgent 执行工具，收集数据、信息。
3. 最后由 ThreatAnalysisAgent 进行汇总分析，给出结论，制定下一步的行动计划。

以上步骤可以循环多次，直到 ThreatAnalysisAgent 判断任务完成为止。

由于数据量较大，收集的数据、信息可能存到本地文件，可以通过 CodeExecutionAgent 编写代码进行 查看、分析 等处理。


## 对话历史：
{conversation_history}

## 当前消息：
{current_message}

基于以上信息，选择最合适的下一个Agent。只返回Agent名称，不要其他内容。"""

            # 调用LLM进行选择
            from autogen_agentchat.messages import UserMessage # type: ignore

            # 打印Agent选择器的LLM请求
            model_name = getattr(self.model_client, 'model', 'unknown_model')
            print_llm_interaction(
                agent_name="AgentSelector",
                model_name=model_name,
                interaction_type="REQUEST",
                content=selector_prompt,
                purpose="Agent选择"
            )

            response = await self.model_client.create(
                messages=[UserMessage(content=selector_prompt, source="User")],
                cancellation_token=CancellationToken()
            )

            selected_agent_name = getattr(response, 'content', str(response))

            # 打印Agent选择器的LLM响应
            print_llm_interaction(
                agent_name="AgentSelector",
                model_name=model_name,
                interaction_type="RESPONSE",
                content=str(selected_agent_name),
                purpose="Agent选择结果"
            )

            if not isinstance(selected_agent_name, str):
                self.logger.warning(f"⚠️ Agent选择器返回了非字符串内容，将使用默认值。返回类型: {type(selected_agent_name)}")
                # print(f"⚠️ Agent选择器返回了非字符串内容，将使用默认值。返回类型: {type(selected_agent_name)}")
                selected_agent_name = ""

            selected_agent = selected_agent_name.strip()

            # 验证选择的Agent是否存在
            if selected_agent in self.agents:
                return selected_agent
            else:
                self.logger.warning(f"⚠️ 选择的Agent '{selected_agent}' 不存在，使用默认Agent")
                # print(f"⚠️ 选择的Agent '{selected_agent}' 不存在，使用默认Agent")
                return available_agents[0] if available_agents else None

        except Exception as e:
            self.logger.error(f"❌ Agent选择失败: {e}")
            # print(f"❌ Agent选择失败: {e}")
            # 返回默认Agent
            available_agents = list(self.agents.keys())
            return available_agents[0] if available_agents else None
    def _format_conversation_history(self, context: GroupChatContext) -> str:
        """格式化对话历史"""
        if not context.messages:
            return "暂无对话历史"

        history_lines = []
        for record in context.messages[-5:]:  # 只显示最近5轮对话
            output_content = getattr(record.output_message, 'content', str(record.output_message)) if record.output_message else '执行中...'
            history_lines.append(f"[{record.agent_name}]: {output_content}")

        return "\n".join(history_lines)

    async def _execute_agent(self, agent_name: str, messages: List[TextMessage],
                           context: GroupChatContext, main_task_manager: MainTaskManager,
                           current_task: SubTaskManager) -> AgentExecutionRecord:
        """执行单个Agent并记录完整过程，包括reasoning和工具调用详情"""
        start_time = datetime.now()
        self.logger.info(f"开始执行Agent: {agent_name}")

        # 获取Agent实例
        agent = self.agents.get(agent_name)
        if not agent:
            self.logger.error(f"Agent '{agent_name}' 不存在")
            raise ValueError(f"Agent '{agent_name}' 不存在")

        # 从主任务管理器中获取当前任务的TaskItem
        current_task_item = main_task_manager.get_task(current_task.main_task_id)
        if not current_task_item:
            self.logger.warning(f"在MainTaskManager中未找到ID为 {current_task.main_task_id} 的任务项，上下文可能不完整")

        # 获取当前请求内容
        current_request = getattr(messages[-1], 'content', str(messages[-1])) if messages else ""

        # 创建执行记录
        execution_record = AgentExecutionRecord(
            agent_name=agent_name,
            agent_role=agent.agent_role,
            input_messages=messages.copy(),
            context_info={"agent_role": agent.agent_role.value, "current_request": current_request},
            timestamp=start_time
        )

        try:
            self.logger.info(f"🤖 执行Agent: {agent_name}")
            # print(f"🤖 执行Agent: {agent_name}")

            # 让Agent自动处理上下文（通过BaseManagedAgent）
            response_stream = agent.on_messages_stream(
                messages=messages,
                cancellation_token=CancellationToken(),
                main_task_manager=main_task_manager,
                current_task=current_task_item  # 传递TaskItem而不是SubTaskManager
            )

            # 收集流式响应中的详细信息
            reasoning_parts = []
            tool_call_requests = []
            tool_call_results = []
            final_message = None

            async for response in response_stream:
                # 提取reasoning和工具调用
                if hasattr(response, 'content') and hasattr(response, '__class__'):
                    # 使用cast来处理linter的类型推断问题
                    response_with_content = cast(Any, response)
                    msg_type = response_with_content.__class__.__name__

                    if 'Thought' in msg_type or 'Reasoning' in msg_type:
                        content = getattr(response_with_content, 'content', str(response_with_content))
                        reasoning_parts.append(str(content))
                        execution_record.message_type = "assistant"

                    elif 'ToolCallRequest' in msg_type:
                        content = getattr(response_with_content, 'content', str(response_with_content))
                        tool_call_requests.append({
                            'type': 'request',
                            'content': str(content),
                            'timestamp': datetime.now().isoformat()
                        })
                        execution_record.message_type = "tool"

                    elif 'ToolCallExecution' in msg_type or 'ToolCallResult' in msg_type:
                        content = getattr(response_with_content, 'content', str(response_with_content))
                        tool_call_results.append({
                            'type': 'result',
                            'content': str(content),
                            'timestamp': datetime.now().isoformat()
                        })
                
                # 获取最终的聊天消息
                if isinstance(response, TextMessage):
                    final_message = response

            # 整理执行记录
            if reasoning_parts:
                execution_record.reasoning_content = "\n".join(reasoning_parts)
                if execution_record.message_type == "assistant":
                    pass  # 已经设置为assistant

            if tool_call_requests or tool_call_results:
                execution_record.tool_calls = tool_call_requests
                execution_record.tool_results = tool_call_results
                if execution_record.message_type == "assistant":
                    execution_record.message_type = "tool"

            # 默认为assistant消息类型
            if execution_record.message_type == "assistant":
                pass  # 保持默认值

            # 设置输出消息
            if isinstance(final_message, TextMessage):
                execution_record.output_message = final_message
            else:
                execution_record.output_message = TextMessage(
                    content="Agent执行完成，但未返回有效的文本消息",
                    source=agent_name
                )

            self.logger.info(f"✅ Agent {agent_name} 执行完成 (类型: {execution_record.message_type})")
            # print(f"✅ Agent {agent_name} 执行完成 (类型: {execution_record.message_type})")

        except Exception as e:
            self.logger.error(f"❌ Agent {agent_name} 执行失败: {e}")
            # print(f"❌ Agent {agent_name} 执行失败: {e}")
            execution_record.message_type = "system"  # 错误消息使用system类型
            execution_record.output_message = TextMessage(
                content=f"执行失败: {str(e)}",
                source=agent_name
            )

        # 计算执行时间
        execution_record.execution_duration = (datetime.now() - start_time).total_seconds()

        # 将对话历史存储到SubTaskManager中
        try:
            # 确保message_type不为None
            message_type = execution_record.message_type or "assistant"

            current_task.add_agent_conversation(
                agent_name=agent_name,
                agent_role=agent.agent_role.value,
                message_type=message_type,
                input_message=current_request,
                output_message=getattr(execution_record.output_message, 'content', str(execution_record.output_message)) if execution_record.output_message else "",
                reasoning_content=execution_record.reasoning_content,
                tool_calls=execution_record.tool_calls,
                execution_duration=execution_record.execution_duration
            )
            self.logger.debug(f"✅ 已将{agent_name}的对话历史存储到SubTaskManager")
        except Exception as e:
            self.logger.warning(f"⚠️ 存储{agent_name}对话历史失败: {e}")
            self.logger.warning(f"   message_type: {execution_record.message_type}")
            self.logger.warning(f"   agent_role: {agent.agent_role.value}")
            import traceback
            self.logger.warning(f"   详细错误: {traceback.format_exc()}")

        return execution_record

    def _get_agent_system_message(self, agent_name: str, agent: Any) -> str:
        """获取Agent的system message"""
        # 尝试从Agent实例获取system message
        if hasattr(agent, '_get_system_message'):
            return agent._get_system_message()
        elif hasattr(agent, '_system_message'):
            return agent._system_message
        else:
            # 默认system message
            return f"你是一个{agent.agent_role.value}Agent，专门负责{agent_name}相关任务的处理。"
    def _build_agent_context(self, agent_name: str, context: GroupChatContext,
                           main_task_manager: MainTaskManager, current_task: SubTaskManager) -> Dict[str, Any]:
        """为特定Agent构建个性化上下文"""
        # 从全局对话历史中提取该Agent的历史记录
        agent_history = [
            {
                "round": i + 1,
                "input": getattr(record.input_messages[-1], 'content', str(record.input_messages[-1])) if record.input_messages else "",
                "output": getattr(record.output_message, 'content', str(record.output_message)) if record.output_message else "",
                "timestamp": record.timestamp.isoformat()
            }
            for i, record in enumerate(context.messages)
            if record.agent_name == agent_name
        ]

        agent_context = {
            "agent_name": agent_name,
            "task_info": context.task_info,
            "agent_history": agent_history,
            "recent_messages": context.messages[-3:] if context.messages else [],
            "main_task_context": {
                "user_request": main_task_manager.user_request,
                "analysis_plan": main_task_manager.analysis_plan,
                "execution_results": main_task_manager.execution_results[-5:] if main_task_manager.execution_results else []
            },
            "current_task_context": {
                "task_title": current_task.task_title,
                "task_id": current_task.main_task_id,
                "recent_activities": current_task.activity_history[-5:] if current_task.activity_history else [],
                "context_summary": current_task.context_summary,
                "analysis_count": current_task.analysis_count,
                "max_analysis_count": current_task.max_analysis_count
            }
        }

        return agent_context

    async def _summarize_conversation(self, context: GroupChatContext) -> str:
        """使用LLM对对话历史进行总结，包含reasoning和工具调用详情"""
        try:
            if not context.messages:
                return "暂无对话内容"

            # 构建详细的对话分析
            conversation_details = []
            message_type_stats = {"user": 0, "assistant": 0, "tool": 0, "system": 0}

            for i, record in enumerate(context.messages, 1):
                # 统计消息类型
                message_type_stats[record.message_type] = message_type_stats.get(record.message_type, 0) + 1

                # 构建详细信息
                details = f"""
=== 轮次 {i}: {record.agent_name} ({record.agent_role.value}) ===
消息类型: {record.message_type}
输入: {getattr(record.input_messages[-1], 'content', str(record.input_messages[-1]))[:200] if record.input_messages else 'N/A'}...
"""

                # 添加reasoning内容
                if record.reasoning_content:
                    details += f"推理过程: {record.reasoning_content[:300]}...\n"

                # 添加工具调用详情
                if record.tool_calls:
                    details += f"工具调用: {len(record.tool_calls)} 次\n"
                    for tool_call in record.tool_calls[:3]:  # 只显示前3个
                        details += f"  - {tool_call.get('type', 'unknown')}: {str(tool_call.get('content', ''))[:100]}...\n"

                if record.tool_results:
                    details += f"工具结果: {len(record.tool_results)} 个\n"
                    for tool_result in record.tool_results[:3]:  # 只显示前3个
                        details += f"  - {tool_result.get('type', 'unknown')}: {str(tool_result.get('content', ''))[:100]}...\n"

                # 添加输出
                output_content = getattr(record.output_message, 'content', str(record.output_message)) if record.output_message else 'N/A'
                details += f"输出: {output_content[:200]}...\n"
                details += f"执行时长: {record.execution_duration:.2f}秒\n"

                conversation_details.append(details)

            # 构建统计信息
            stats_info = f"""
消息类型统计:
- 用户消息: {message_type_stats.get('user', 0)} 次
- 助手消息: {message_type_stats.get('assistant', 0)} 次
- 工具消息: {message_type_stats.get('tool', 0)} 次
- 系统消息: {message_type_stats.get('system', 0)} 次
总轮次: {len(context.messages)}
"""

            summary_prompt = f"""请对以下Agent协作对话进行深度总结分析：

## 任务背景：
- 任务标题：{context.task_info.get('current_task_title', 'N/A')}
- 用户请求：{context.task_info.get('user_request', 'N/A')}

## 执行统计：
{stats_info}

## 详细对话记录：
{chr(10).join(conversation_details)}

请从以下角度进行总结：
1. **执行流程分析**: 整体的执行步骤和逻辑流程
2. **Agent协作效果**: 各Agent的分工协作情况和效果评估
3. **推理质量评估**: 如果有推理过程，评估推理的逻辑性和有效性
4. **工具使用分析**: 工具调用的合理性、成功率和结果质量
5. **关键发现总结**: 重要的分析结果、发现的问题或威胁
6. **改进建议**: 流程优化、工具改进或协作改进的建议

请用中文回答，保持结构清晰、内容详实。"""

            from autogen_agentchat.messages import UserMessage # type: ignore

            # 打印对话总结器的LLM请求
            model_name = 'unknown_model'
            model_client = getattr(self, 'model_client', None) or getattr(self, '_model_client', None)
            if model_client:
                if hasattr(model_client, 'model'):
                    model_name = model_client.model
                elif hasattr(model_client, 'model_info'):
                    model_info = model_client.model_info
                    if isinstance(model_info, dict) and 'model' in model_info:
                        model_name = model_info['model']
            print_llm_interaction(
                agent_name="ConversationSummarizer",
                model_name=model_name,
                interaction_type="REQUEST",
                content=summary_prompt,
                purpose="对话总结"
            )

            response = await self.model_client.create(
                messages=[UserMessage(content=summary_prompt, source="User")],
                cancellation_token=CancellationToken()
            )

            # 打印对话总结器的LLM响应
            response_content = getattr(response, 'content', str(response))
            summary_content = response_content if isinstance(response_content, str) else str(response_content)
            print_llm_interaction(
                agent_name="ConversationSummarizer",
                model_name=model_name,
                interaction_type="RESPONSE",
                content=summary_content,
                purpose="对话总结结果"
            )

            if isinstance(response_content, str):
                return response_content
            else:
                self.logger.error(f"❌ 对话总结返回了非预期的格式: {type(response_content)}")
                # print(f"❌ 对话总结返回了非预期的格式: {type(response_content)}")
                return "总结失败: 返回格式错误"

        except Exception as e:
            self.logger.error(f"❌ 对话总结失败: {e}")
            # print(f"❌ 对话总结失败: {e}")
            return f"总结失败: {str(e)}"

    async def run_subtask_collaboration(self,
                                       initial_message: str,
                                       main_task_manager: MainTaskManager,
                                       current_task: SubTaskManager) -> Dict[str, Any]:
        """
        运行完全自主的Agent协作流程
        """
        self.logger.info("🚀 开始自主Agent协作...")
        # print("🚀 开始自主Agent协作...")

        # 1. 初始化上下文
        context = self._initialize_context(main_task_manager, current_task)
        self.current_context = context

        # 2. 创建初始消息
        current_message = initial_message
        messages = [TextMessage(content=current_message, source="User")]

        # 3. 主协作循环
        for round_num in range(self.max_rounds):
            self.logger.info(f"🔄 协作轮次 {round_num + 1}/{self.max_rounds}")
            # print(f"🔄 协作轮次 {round_num + 1}/{self.max_rounds}")

            # 选择下一个Agent
            selected_agent = await self._select_next_agent(context, current_message)
            if not selected_agent:
                self.logger.warning("⚠️ 无法选择Agent，结束协作")
                # print("⚠️ 无法选择Agent，结束协作")
                break

            # 执行Agent
            execution_record = await self._execute_agent(
                selected_agent, messages, context, main_task_manager, current_task
            )

            # 更新上下文
            context.messages.append(execution_record)

            # 检查是否应该结束
            if execution_record.output_message:
                output_content = getattr(execution_record.output_message, 'content', str(execution_record.output_message))
                if "Termination" in output_content:
                    self.logger.info("🏁 检测到终止信号，结束协作")
                    # print("🏁 检测到终止信号，结束协作")
                    break

            # 准备下一轮消息
            if execution_record.output_message:
                current_message = getattr(execution_record.output_message, 'content', str(execution_record.output_message))
                messages = [execution_record.output_message]

        # 4. 生成对话总结
        context.conversation_summary = await self._summarize_conversation(context)
        context.updated_at = datetime.now()

        # 4.1 将对话摘要更新到SubTaskManager
        try:
            current_task.update_context_summary(context.conversation_summary)
            self.logger.debug("✅ 已将对话摘要更新到SubTaskManager")
        except Exception as e:
            self.logger.warning(f"⚠️ 更新SubTaskManager摘要失败: {e}")

        # 5. 自动存储记忆摘要
        await self._store_collaboration_memories(context, main_task_manager, current_task)

        self.logger.info("✅ 自主Agent协作完成")
        # print("✅ 自主Agent协作完成")

        # 6. 返回完整结果，包含详细的对话类型和内容
        return {
            "context": context,
            "messages": [
                {
                    "round": i + 1,
                    "agent_name": record.agent_name,
                    "agent_role": record.agent_role.value,
                    "message_type": record.message_type,
                    "input": getattr(record.input_messages[-1], 'content', str(record.input_messages[-1])) if record.input_messages else "",
                    "output": getattr(record.output_message, 'content', str(record.output_message)) if record.output_message else "",
                    "reasoning_content": record.reasoning_content,
                    "tool_calls": record.tool_calls,
                    "tool_results": record.tool_results,

                    "execution_duration": record.execution_duration,
                    "timestamp": record.timestamp.isoformat(),
                    "formatted_summary": record.get_formatted_summary()
                }
                for i, record in enumerate(context.messages)
            ],
            "conversation_summary": context.conversation_summary,
            "execution_statistics": {
                "total_rounds": len(context.messages),
                "message_types": {
                    "user": sum(1 for r in context.messages if r.message_type == "user"),
                    "assistant": sum(1 for r in context.messages if r.message_type == "assistant"),
                    "tool": sum(1 for r in context.messages if r.message_type == "tool"),
                    "system": sum(1 for r in context.messages if r.message_type == "system")
                }
            },
            "session_id": context.session_id,
            "task_info": context.task_info
        }

    def get_agent_by_role(self, role: AgentRole) -> Optional[BaseManagedAgent]:
        """根据角色获取Agent"""
        for agent in self.agents.values():
            if isinstance(agent, BaseManagedAgent) and agent.agent_role == role:
                return agent
        return None

    async def _store_collaboration_memories(self, context: GroupChatContext,
                                          main_task_manager: MainTaskManager,
                                          current_task: SubTaskManager):
        """
        自动存储协作过程的记忆摘要

        将Agent协作的结果存储为记忆，供后续任务参考
        """
        try:
            from src.extensions.memory.integration_adapter import MemoryIntegrationAdapter
            memory_adapter = MemoryIntegrationAdapter()

            # 1. 为每个参与的Agent存储个人记忆
            # 从消息历史中提取各Agent的参与记录
            agent_participation = {}
            for record in context.messages:
                agent_name = record.agent_name
                if agent_name not in agent_participation:
                    agent_participation[agent_name] = []
                agent_participation[agent_name].append({
                    "input": getattr(record.input_messages[-1], 'content', str(record.input_messages[-1])) if record.input_messages else "",
                    "output": getattr(record.output_message, 'content', str(record.output_message)) if record.output_message else "",
                    "timestamp": record.timestamp.isoformat()
                })

            for agent_name, agent_history in agent_participation.items():
                if agent_history:  # 只为有参与的Agent存储记忆
                    # 构建Agent个人记忆内容
                    agent_memory_content = self._build_agent_memory_content(
                        agent_name, agent_history, context
                    )

                    # 存储Agent个人记忆
                    success = memory_adapter.save_agent_interaction(
                        agent_id=agent_name,
                        user_input=f"任务: {context.task_info.get('current_task_title', 'N/A')}",
                        agent_response=agent_memory_content,
                        reasoning=f"协作轮次: {len(agent_history)}, 任务ID: {context.task_info.get('current_task_id', 'N/A')}"
                    )

                    if success:
                        self.logger.info(f"💾 已为Agent {agent_name} 存储记忆")
                    else:
                        self.logger.warning(f"⚠️ Agent {agent_name} 记忆存储失败")

            # 2. 存储任务级别的全局记忆
            task_memory_content = self._build_task_memory_content(context, main_task_manager, current_task)

            # 使用任务ID作为Agent ID存储任务记忆
            task_agent_id = f"task_{current_task.main_task_id}"
            success = memory_adapter.save_agent_interaction(
                agent_id=task_agent_id,
                user_input=main_task_manager.user_request,
                agent_response=task_memory_content,
                reasoning=context.conversation_summary
            )

            if success:
                self.logger.info(f"💾 已存储任务 {current_task.task_title} 的记忆")
            else:
                self.logger.warning(f"⚠️ 任务记忆存储失败")

        except Exception as e:
            self.logger.error(f"❌ 记忆存储过程失败: {e}")

    def _build_agent_memory_content(self, agent_name: str, agent_history: List[Dict],
                                   context: GroupChatContext) -> str:
        """构建Agent个人记忆内容"""
        memory_parts = [
            f"Agent: {agent_name}",
            f"任务: {context.task_info.get('current_task_title', 'N/A')}",
            f"参与轮次: {len(agent_history)}",
            "",
            "执行记录:"
        ]

        for i, history_item in enumerate(agent_history, 1):
            memory_parts.append(f"轮次 {i}:")
            memory_parts.append(f"  输入: {history_item.get('input', 'N/A')[:100]}...")
            memory_parts.append(f"  输出: {history_item.get('output', 'N/A')[:100]}...")
            memory_parts.append(f"  时间: {history_item.get('timestamp', 'N/A')}")
            memory_parts.append("")

        return "\n".join(memory_parts)

    def _build_task_memory_content(self, context: GroupChatContext,
                                  main_task_manager: MainTaskManager,
                                  current_task: SubTaskManager) -> str:
        """构建任务级别记忆内容"""
        # 从消息历史中提取参与的Agent
        participating_agents = list(set(record.agent_name for record in context.messages))

        memory_parts = [
            f"任务标题: {current_task.task_title}",
            f"任务ID: {current_task.main_task_id}",
            f"用户请求: {main_task_manager.user_request}",
            f"分析计划: {main_task_manager.analysis_plan or 'N/A'}",
            "",
            "协作统计:",
            f"  总轮次: {len(context.messages)}",
            f"  参与Agent: {', '.join(participating_agents)}",
            "",
            "执行摘要:",
            context.conversation_summary[:500] + "..." if len(context.conversation_summary) > 500 else context.conversation_summary
        ]

        return "\n".join(memory_parts)
