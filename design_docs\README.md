# 🏗️ 网络安全Multi-Agent智能分析平台 - 设计文档

本目录包含系统的完整设计文档和架构图。

## 📁 目录结构

```
design_docs/
├── README.md                           # 本文件
├── 00_design_overview.md               # 设计文档总览
├── 01_system_architecture.md           # 系统架构设计
├── 02_agent_flow_design.md            # Agent流转流程设计
├── 03_context_management_design.md    # 上下文管理架构设计
├── 04_task_management_design.md       # 任务管理架构设计
├── 05_component_analysis.md           # 核心组件分析
├── diagrams/                          # 架构图目录
│   ├── design_docs_structure.mermaid  # 设计文档结构图
│   ├── system_architecture.mermaid    # 系统架构图 (详细版)
│   ├── system_architecture_simple.mermaid # 系统架构图 (简化版)
│   ├── agent_flow.mermaid             # Agent流转流程图
│   ├── context_management.mermaid     # 上下文管理架构图
│   ├── task_management.mermaid        # 任务管理架构图
│   └── context_flow.mermaid           # 上下文流转流程图
└── implementation/                    # 实现细节
    ├── agent_configs.md               # Agent配置说明
    ├── tool_integration.md            # 工具集成设计
    └── user_interface.md              # 用户界面设计
```

## 🎯 设计原则

1. **模块化设计**: 清晰的分层架构，职责分离
2. **流程驱动**: 严格按照README定义的6步分析流程
3. **智能协作**: 在需要时进行Agent智能选择
4. **记忆管理**: 完善的长短时记忆和知识管理
5. **可扩展性**: 支持新Agent和工具的动态扩展

## 📊 核心架构

系统采用分层架构设计：
- **用户交互层**: 统一的用户界面抽象
- **应用控制层**: 分析流程控制和用户交互代理
- **Agent协作层**: 6个专业化Agent的智能协作
- **核心服务层**: 任务管理、记忆管理、工具管理
- **基础设施层**: 配置管理、模型客户端、持久化存储

## 🔄 分析流程

严格按照6步流程执行：
1. 用户输入任务/指令
2. MemoryAgent查找相关参考
3. PlanningAgent制定分析计划
4. 主任务执行循环（包含子任务执行循环）
5. ReportAgent生成分析报告
6. MemoryAgent存储知识到长期记忆

## 📝 文档说明

每个设计文档都包含：
- 详细的架构说明
- Mermaid图表
- 实现要点
- 关键接口定义
- 最佳实践建议

## 📊 设计文档结构图

完整的设计文档结构和关系可以通过以下图表查看：
- `diagrams/design_docs_structure.mermaid` - 设计文档结构图

该图表展示了：
- 📁 所有设计文档的组织结构
- 🏗️ 系统架构的分层关系
- 🔄 核心分析流程的步骤
- 🎯 设计原则和理念

## 🚀 快速开始

1. **首先阅读**: `00_design_overview.md` - 获得完整的设计文档概览
2. **结构理解**: 查看 `diagrams/design_docs_structure.mermaid` 了解文档整体结构
3. **架构掌握**: 阅读 `01_system_architecture.md` 了解整体架构
4. **流程理解**: 查看 `02_agent_flow_design.md` 理解Agent交互流程
5. **图表参考**: 参考 `diagrams/` 目录中的可视化图表
6. **实现指导**: 根据 `implementation/` 目录中的文档进行具体实现

---

**版本**: v1.0  
**更新时间**: 2024-12-19  
**维护者**: 系统架构团队
