"""
威胁情报查询工具实现

提供威胁情报在线查询功能，支持IP、域名、CVE漏洞、文件hash、URL、APT攻击组织等威胁情报查询。
基于NTI接口进行威胁情报查询，简化了复杂的解析规则，通过prompt让模型提供符合要求的参数。
"""

import asyncio
import base64
import json
import os
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable, Literal
from typing_extensions import Annotated
import urllib3
from src.utils.logger import get_logger

# 禁用SSL警告
# urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 全局日志记录器
logger = get_logger("threat_intelligence_tools")

# NTI接口配置
NTI_BASE_URL = "https://***********:18045/api/v3"
NTI_HEADERS = {
    "Accept": "*/*",
    "lab_name": "tianshu_lab",
    "Accept-encoding": "gzip",
    "Content-Type": "application/json"
}

# 威胁情报查询结果保存目录
RESULTS_DIR = os.path.join("data", "threat_intel_results")


def _ensure_results_directory():
    """确保结果保存目录存在"""
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR, exist_ok=True)


def _save_threat_intel_results(result: Dict[str, Any], query_type: str) -> str:
    """
    保存威胁情报查询结果到文件
    
    :param result: 查询结果字典
    :param query_type: 查询类型
    :return: 保存的文件路径
    """
    _ensure_results_directory()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"threat_intel_{query_type}_{timestamp}.json"
    filepath = os.path.join(RESULTS_DIR, filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        return filepath
    except Exception as e:
        logger.error(f"⚠️ [Tool] 保存结果文件失败: {e}")
        # print(f"⚠️ [Tool] 保存结果文件失败: {e}")
        return ""


def _base64_encode(string: str) -> str:
    """
    Base64编码字符串，用于URL查询
    
    :param string: 要编码的字符串
    :return: Base64编码后的字符串
    """
    encoded = base64.urlsafe_b64encode(str.encode(string))
    return encoded.decode()


async def query_ip_threat_intel(
    ip_address: Annotated[str, "要查询的IP地址 (例如: '*************', '*******')"],
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    查询IP地址的威胁情报信息
    
    :param ip_address: 要查询的IP地址
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始查询IP威胁情报: {ip_address}")
    # print(f"🔍 [Tool] 开始查询IP威胁情报: {ip_address}")

    try:
        url = f"{NTI_BASE_URL}/ip/label/"
        response = requests.get(
            url, 
            params={"query": ip_address}, 
            headers=NTI_HEADERS, 
            verify=False,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # 构建查询结果
            query_result = {
                "query_type": "ip",
                "query_target": ip_address,
                "query_time": datetime.now().isoformat(),
                "status": "success",
                "data": result
            }
            
            # 保存结果到文件
            filepath = _save_threat_intel_results(query_result, "ip")
            query_result["saved_to"] = filepath
            
            logger.info(f"✅ [Tool] IP威胁情报查询完成: {ip_address}")
            # print(f"✅ [Tool] IP威胁情报查询完成: {ip_address}")
            return query_result
            
        else:
            error_msg = f"API请求失败，状态码: {response.status_code}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "ip",
                "query_target": ip_address,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }
            
    except Exception as e:
        error_msg = f"查询IP威胁情报失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "ip",
            "query_target": ip_address,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


async def query_domain_threat_intel(
    domain: Annotated[str, "要查询的域名 (例如: 'example.com', 'malicious-site.net')"],
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    查询域名的威胁情报信息
    
    :param domain: 要查询的域名
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始查询域名威胁情报: {domain}")
    # print(f"🔍 [Tool] 开始查询域名威胁情报: {domain}")
    
    try:
        url = f"{NTI_BASE_URL}/domain/label/"
        # 清理域名格式
        clean_domain = domain.replace("[", "").replace("]", "").split(":")[0]
        
        response = requests.get(
            url, 
            params={"query": clean_domain}, 
            headers=NTI_HEADERS, 
            verify=False,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # 构建查询结果
            query_result = {
                "query_type": "domain",
                "query_target": domain,
                "cleaned_domain": clean_domain,
                "query_time": datetime.now().isoformat(),
                "status": "success",
                "data": result
            }
            
            # 保存结果到文件
            filepath = _save_threat_intel_results(query_result, "domain")
            query_result["saved_to"] = filepath
            
            logger.info(f"✅ [Tool] 域名威胁情报查询完成: {domain}")
            # print(f"✅ [Tool] 域名威胁情报查询完成: {domain}")
            return query_result
            
        else:
            error_msg = f"API请求失败，状态码: {response.status_code}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "domain",
                "query_target": domain,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }
            
    except Exception as e:
        error_msg = f"查询域名威胁情报失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "domain",
            "query_target": domain,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


async def query_url_threat_intel(
    url: Annotated[str, "要查询的URL (例如: 'http://example.com/path', 'https://malicious-site.net')"],
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    查询URL的威胁情报信息

    :param url: 要查询的URL
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始查询URL威胁情报: {url}")
    # print(f"🔍 [Tool] 开始查询URL威胁情报: {url}")

    try:
        api_url = f"{NTI_BASE_URL}/domain/label/"
        # 清理URL格式并进行Base64编码
        clean_url = url.replace("[", "").replace("]", "")
        encoded_url = _base64_encode(clean_url)

        response = requests.get(
            api_url,
            params={"query": encoded_url},
            headers=NTI_HEADERS,
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()

            # 构建查询结果
            query_result = {
                "query_type": "url",
                "query_target": url,
                "encoded_url": encoded_url,
                "query_time": datetime.now().isoformat(),
                "status": "success",
                "data": result
            }

            # 保存结果到文件
            filepath = _save_threat_intel_results(query_result, "url")
            query_result["saved_to"] = filepath

            logger.info(f"✅ [Tool] URL威胁情报查询完成: {url}")
            # print(f"✅ [Tool] URL威胁情报查询完成: {url}")
            return query_result

        else:
            error_msg = f"API请求失败，状态码: {response.status_code}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "url",
                "query_target": url,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"查询URL威胁情报失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "url",
            "query_target": url,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


async def query_file_hash_threat_intel(
    file_hash: Annotated[str, "要查询的文件哈希值 (支持MD5/SHA1/SHA256，例如: 'd41d8cd98f00b204e9800998ecf8427e')"],
    hash_type: Annotated[Literal["auto", "md5", "sha1", "sha256"], "哈希类型 (auto为自动识别)"] = "auto",
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    查询文件哈希的威胁情报信息

    :param file_hash: 要查询的文件哈希值
    :param hash_type: 哈希类型
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始查询文件哈希威胁情报: {file_hash}")
    # print(f"🔍 [Tool] 开始查询文件哈希威胁情报: {file_hash}")

    try:
        url = f"{NTI_BASE_URL}/object/file-label/"

        response = requests.get(
            url,
            params={"query": file_hash},
            headers=NTI_HEADERS,
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()

            # 构建查询结果
            query_result = {
                "query_type": "file_hash",
                "query_target": file_hash,
                "hash_type": hash_type,
                "query_time": datetime.now().isoformat(),
                "status": "success",
                "data": result
            }

            # 保存结果到文件
            filepath = _save_threat_intel_results(query_result, "file_hash")
            query_result["saved_to"] = filepath

            logger.info(f"✅ [Tool] 文件哈希威胁情报查询完成: {file_hash}")
            # print(f"✅ [Tool] 文件哈希威胁情报查询完成: {file_hash}")
            return query_result

        else:
            error_msg = f"API请求失败，状态码: {response.status_code}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "file_hash",
                "query_target": file_hash,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"查询文件哈希威胁情报失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "file_hash",
            "query_target": file_hash,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


async def query_cve_threat_intel(
    cve_id: Annotated[str, "要查询的CVE漏洞编号 (例如: 'CVE-2021-44228', 'CVE-2023-23397')"],
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    查询CVE漏洞的威胁情报信息

    :param cve_id: 要查询的CVE漏洞编号
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始查询CVE漏洞威胁情报: {cve_id}")
    # print(f"🔍 [Tool] 开始查询CVE漏洞威胁情报: {cve_id}")

    try:
        url = f"{NTI_BASE_URL}/object/vul/"

        response = requests.get(
            url,
            params={"query": cve_id},
            headers=NTI_HEADERS,
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()

            # 简化结果，移除过大的字段
            if "data" in result and isinstance(result["data"], dict):
                data = result["data"]
                # 移除可能很大的字段
                for field in ["affected_softwares", "configurations", "related"]:
                    if field in data:
                        del data[field]

            # 构建查询结果
            query_result = {
                "query_type": "cve",
                "query_target": cve_id,
                "query_time": datetime.now().isoformat(),
                "status": "success",
                "data": result
            }

            # 保存结果到文件
            filepath = _save_threat_intel_results(query_result, "cve")
            query_result["saved_to"] = filepath

            logger.info(f"✅ [Tool] CVE漏洞威胁情报查询完成: {cve_id}")
            # print(f"✅ [Tool] CVE漏洞威胁情报查询完成: {cve_id}")
            return query_result

        else:
            error_msg = f"API请求失败，状态码: {response.status_code}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "cve",
                "query_target": cve_id,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"查询CVE漏洞威胁情报失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "cve",
            "query_target": cve_id,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


async def query_apt_threat_intel(
    apt_name: Annotated[str, "要查询的APT攻击组织名称或关键词 (例如: 'APT1', 'Lazarus', 'Equation Group')"],
    search_type: Annotated[Literal["exact", "fuzzy"], "搜索类型 (exact为精确匹配，fuzzy为模糊搜索)"] = "fuzzy",
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    查询APT攻击组织的威胁情报信息

    :param apt_name: 要查询的APT攻击组织名称或关键词
    :param search_type: 搜索类型
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始查询APT组织威胁情报: {apt_name}")
    # print(f"🔍 [Tool] 开始查询APT组织威胁情报: {apt_name}")

    try:
        # 使用Elasticsearch接口查询APT组织信息
        es_url = "***********************@***********:9200/threatormalwre1/_search"

        query = {
            "query": {
                "bool": {
                    "should": [
                        {"match": {"properties.name": apt_name}},
                        {"match": {"label": "threat-actor"}}
                    ]
                }
            },
            "size": 5
        }

        es_headers = {
            "Accept": "*/*",
            "Accept-encoding": "gzip",
            "Content-Type": "application/json"
        }

        response = requests.post(
            es_url,
            data=json.dumps(query),
            headers=es_headers,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()

            # 构建查询结果
            query_result = {
                "query_type": "apt",
                "query_target": apt_name,
                "search_type": search_type,
                "query_time": datetime.now().isoformat(),
                "status": "success",
                "data": result
            }

            # 如果有查询结果，提取第一个匹配项
            if result.get("hits", {}).get("hits"):
                query_result["apt_info"] = result["hits"]["hits"][0]["_source"]

            # 保存结果到文件
            filepath = _save_threat_intel_results(query_result, "apt")
            query_result["saved_to"] = filepath

            logger.info(f"✅ [Tool] APT组织威胁情报查询完成: {apt_name}")
            # print(f"✅ [Tool] APT组织威胁情报查询完成: {apt_name}")
            return query_result

        else:
            error_msg = f"Elasticsearch请求失败，状态码: {response.status_code}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "apt",
                "query_target": apt_name,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"查询APT组织威胁情报失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "apt",
            "query_target": apt_name,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


async def query_threat_intelligence(
    indicator: Annotated[str, "要查询的威胁指标 (IP地址、域名、URL、文件哈希、CVE编号或APT组织名称)"],
    indicator_type: Annotated[Literal["auto", "ip", "domain", "url", "file_hash", "cve", "apt"], "指标类型 (auto为自动识别)"] = "auto",
    include_details: Annotated[bool, "是否包含详细信息 (默认True)"] = True
) -> Dict[str, Any]:
    """
    通用威胁情报查询工具，支持多种类型的威胁指标查询

    :param indicator: 要查询的威胁指标
    :param indicator_type: 指标类型
    :param include_details: 是否包含详细信息
    :return: 包含威胁情报信息的字典
    """
    logger.info(f"🔍 [Tool] 开始通用威胁情报查询: {indicator} (类型: {indicator_type})")
    # print(f"🔍 [Tool] 开始通用威胁情报查询: {indicator} (类型: {indicator_type})")

    try:
        # 如果是自动识别，根据指标特征判断类型
        if indicator_type == "auto":
            indicator_type = _detect_indicator_type(indicator)
            logger.info(f"🔍 [Tool] 自动识别指标类型: {indicator_type}")
            # print(f"🔍 [Tool] 自动识别指标类型: {indicator_type}")

        # 根据类型调用相应的查询函数
        if indicator_type == "ip":
            return await query_ip_threat_intel(indicator, include_details)
        elif indicator_type == "domain":
            return await query_domain_threat_intel(indicator, include_details)
        elif indicator_type == "url":
            return await query_url_threat_intel(indicator, include_details)
        elif indicator_type == "file_hash":
            return await query_file_hash_threat_intel(indicator, "auto", include_details)
        elif indicator_type == "cve":
            return await query_cve_threat_intel(indicator, include_details)
        elif indicator_type == "apt":
            return await query_apt_threat_intel(indicator, "fuzzy", include_details)
        else:
            error_msg = f"不支持的指标类型: {indicator_type}"
            logger.error(f"❌ [Tool] {error_msg}")
            # print(f"❌ [Tool] {error_msg}")
            return {
                "query_type": "unknown",
                "query_target": indicator,
                "query_time": datetime.now().isoformat(),
                "status": "error",
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"通用威胁情报查询失败: {str(e)}"
        logger.error(f"❌ [Tool] {error_msg}")
        # print(f"❌ [Tool] {error_msg}")
        return {
            "query_type": "unknown",
            "query_target": indicator,
            "query_time": datetime.now().isoformat(),
            "status": "error",
            "error": error_msg
        }


def _detect_indicator_type(indicator: str) -> Literal["ip", "domain", "url", "file_hash", "cve", "apt"]:
    """
    自动检测威胁指标类型

    :param indicator: 威胁指标
    :return: 检测到的指标类型
    """
    import re

    # IP地址检测
    ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    if re.match(ip_pattern, indicator):
        return "ip"

    # CVE编号检测
    cve_pattern = r'^CVE-\d{4}-\d{3,7}$'
    if re.match(cve_pattern, indicator, re.IGNORECASE):
        return "cve"

    # 文件哈希检测 (MD5: 32位, SHA1: 40位, SHA256: 64位)
    if re.match(r'^[a-fA-F0-9]{32}$', indicator):  # MD5
        return "file_hash"
    elif re.match(r'^[a-fA-F0-9]{40}$', indicator):  # SHA1
        return "file_hash"
    elif re.match(r'^[a-fA-F0-9]{64}$', indicator):  # SHA256
        return "file_hash"

    # URL检测
    url_pattern = r'^https?://'
    if re.match(url_pattern, indicator, re.IGNORECASE):
        return "url"

    # 域名检测
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    if re.match(domain_pattern, indicator) and '.' in indicator:
        return "domain"

    # 默认为APT组织名称
    return "apt"


def get_threat_intelligence_tool_functions() -> List[Callable]:
    """
    获取所有威胁情报查询工具函数

    :return: 包含所有威胁情报工具函数的列表
    """
    return [
        # query_threat_intelligence,
        query_ip_threat_intel,
        query_domain_threat_intel,
        query_url_threat_intel,
        query_file_hash_threat_intel,
        query_cve_threat_intel,
        query_apt_threat_intel
    ]
