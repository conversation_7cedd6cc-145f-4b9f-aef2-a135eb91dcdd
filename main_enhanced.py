"""
增强版主应用入口

集成所有新的组件：
1. 分析流程控制器
2. 任务管理系统
3. 记忆管理系统
4. Agent交互协调
5. 工具接入框架
6. 用户交互接口
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from src.core.analysis_flow_controller import AnalysisFlowController
from src.core.user_interface import initialize_default_ui, get_ui_manager
from src.core.user_interaction_proxy import run_interactive_analysis
from src.core.user_proxies import CommandLineUserProxy
from src.utils.logger import get_logger


class EnhancedSecurityAnalysisApp:
    """增强版安全分析应用"""

    def __init__(self):
        self.analysis_controller = None
        self.ui_manager = None
        self.initialized = False
        self.logger = get_logger("main_app")
        
    async def initialize(self) -> bool:
        """初始化应用"""
        try:
            self.logger.info("🚀 正在初始化增强版安全分析系统...")
            # print("🚀 正在初始化增强版安全分析系统...")

            # 1. 初始化用户界面
            self.logger.info("📱 初始化用户界面...")
            # print("📱 初始化用户界面...")
            if not await initialize_default_ui():
                self.logger.error("❌ 用户界面初始化失败")
                # print("❌ 用户界面初始化失败")
                return False
            
            self.ui_manager = get_ui_manager()
            current_ui = await self.ui_manager.get_current_interface()
            
            if current_ui:
                await current_ui.info("🔄 正在初始化系统组件...", "系统启动")
            
            # 2. 创建核心组件 (新顺序)
            self.logger.info("🏗️ 正在创建核心组件...")
            # print("🏗️ 正在创建核心组件...")
            user_proxy = CommandLineUserProxy()
            self.logger.info("✅ UserProxy创建完成")
            # print("   ✅ UserProxy创建完成")

            self.analysis_controller = AnalysisFlowController(user_proxy)
            self.logger.info("✅ 分析流程控制器创建完成")
            # print("   ✅ 分析流程控制器创建完成")

            self.initialized = True

            if current_ui:
                await current_ui.success(
                    "✅ 增强版安全分析系统初始化完成！\n"
                    "🎉 系统已就绪，可以开始安全分析任务。",
                    "✅ 初始化完成"
                )

            self.logger.info("✅ 增强版安全分析系统初始化完成")
            # print("✅ 增强版安全分析系统初始化完成")
            return True
            
        except Exception as e:
            error_msg = f"系统初始化失败: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            # print(f"❌ {error_msg}")

            if self.ui_manager:
                current_ui = await self.ui_manager.get_current_interface()
                if current_ui:
                    await current_ui.error(error_msg, "❌ 初始化错误")

            return False
    
    async def run_interactive_mode(self) -> bool:
        """运行交互模式"""
        if not self.initialized:
            self.logger.error("❌ 系统未初始化，无法运行")
            # print("❌ 系统未初始化，无法运行")
            return False

        try:
            self.logger.info("🎯 启动交互式分析模式...")
            # print("🎯 启动交互式分析模式...")

            assert self.analysis_controller is not None
            # 运行交互式分析
            success, error = await run_interactive_analysis(self.analysis_controller)

            if success:
                self.logger.info("✅ 交互式分析完成")
                # print("✅ 交互式分析完成")
                return True
            else:
                self.logger.error(f"❌ 交互式分析失败: {error}")
                # print(f"❌ 交互式分析失败: {error}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 交互模式运行失败: {e}")
            # print(f"❌ 交互模式运行失败: {e}")
            return False
    
    async def run_batch_analysis(self, user_request: str) -> bool:
        """运行批处理分析"""
        if not self.initialized:
            self.logger.error("❌ 系统未初始化，无法运行")
            # print("❌ 系统未初始化，无法运行")
            return False

        try:
            self.logger.info(f"🎯 启动批处理分析: {user_request}")
            # print(f"🎯 启动批处理分析: {user_request}")

            assert self.analysis_controller is not None
            # 直接运行分析
            success, error = await self.analysis_controller.start_analysis(user_request)

            if success:
                self.logger.info("✅ 批处理分析完成")
                # print("✅ 批处理分析完成")

                # 显示结果
                session = self.analysis_controller.get_current_session()
                if session and session.final_report:
                    # print("\n" + "="*60)
                    # print("📊 分析结果")
                    # print("="*60)
                    # print(session.final_report)
                    # print("="*60)
                    self.logger.info("="*60)
                    self.logger.info("📊 分析结果")
                    self.logger.info("="*60)
                    self.logger.info(session.final_report)
                    self.logger.info("="*60)

                return True
            else:
                self.logger.error(f"❌ 批处理分析失败: {error}")
                # print(f"❌ 批处理分析失败: {error}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 批处理分析运行失败: {e}")
            # print(f"❌ 批处理分析运行失败: {e}")
            return False
    
    async def get_system_status(self) -> dict:
        """获取系统状态"""
        status: Dict[str, Any] = {
            "initialized": self.initialized,
            "analysis_controller": self.analysis_controller is not None,
            "ui_manager": self.ui_manager is not None
        }
        
        if self.initialized:
            assert self.analysis_controller is not None
            # 获取Agent统计
            if self.analysis_controller:
                # 注意：这里可能需要一个更好的方式来获取所有agent的数量
                agent_count = len(self.analysis_controller.flow_level_agents) + len(self.analysis_controller.enhanced_groupchat.agents)
                status["agent_count"] = agent_count
        
        return status
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("🧹 正在清理系统资源...")
            # print("🧹 正在清理系统资源...")
            
            if self.ui_manager:
                await self.ui_manager.cleanup_all()
            
            # Agent的清理逻辑现在可能需要由AnalysisFlowController处理（如果需要）
            
            self.logger.info("✅ 系统资源清理完成")
            # print("✅ 系统资源清理完成")
            
        except Exception as e:
            self.logger.error(f"⚠️ 资源清理时出现错误: {e}")
            # print(f"⚠️ 资源清理时出现错误: {e}")


async def main():
    """主函数"""
    app = EnhancedSecurityAnalysisApp()
    
    try:
        # 初始化应用
        if not await app.initialize():
            app.logger.error("❌ 应用初始化失败")
            # print("❌ 应用初始化失败")
            return 1
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            # 批处理模式
            user_request = " ".join(sys.argv[1:])
            success = await app.run_batch_analysis(user_request)
            return 0 if success else 1
        else:
            # 交互模式
            success = await app.run_interactive_mode()
            return 0 if success else 1
    
    except KeyboardInterrupt:
        app.logger.info("\n👋 用户中断，正在退出...")
        # print("\n👋 用户中断，正在退出...")
        return 0
    except Exception as e:
        app.logger.error(f"❌ 应用运行失败: {e}")
        # print(f"❌ 应用运行失败: {e}")
        return 1
    finally:
        await app.cleanup()


if __name__ == "__main__":
    # 运行应用
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
