graph TB
    subgraph "📁 设计文档目录结构"
        README[📄 README.md<br/>设计文档总览]
        
        subgraph "📋 核心设计文档"
            ARCH[📄 01_system_architecture.md<br/>系统架构设计]
            FLOW[📄 02_agent_flow_design.md<br/>Agent流转流程设计]
            CONTEXT[📄 03_context_management_design.md<br/>上下文管理架构设计]
            TASK[📄 04_task_management_design.md<br/>任务管理架构设计]
            COMP[📄 05_component_analysis.md<br/>核心组件分析]
        end
        
        subgraph "📊 架构图表"
            ARCH_DIAGRAM[🎨 system_architecture.mermaid<br/>系统架构图]
            FLOW_DIAGRAM[🎨 agent_flow.mermaid<br/>Agent流转流程图]
            CONTEXT_DIAGRAM[🎨 context_management.mermaid<br/>上下文管理架构图]
            TASK_DIAGRAM[🎨 task_management.mermaid<br/>任务管理架构图]
            FLOW_CONTEXT[🎨 context_flow.mermaid<br/>上下文流转流程图]
            DESIGN_STRUCTURE[🎨 design_docs_structure.mermaid<br/>设计文档结构图]
        end
        
        subgraph "🔧 实现指南"
            AGENT_CONFIG[📄 agent_configs.md<br/>Agent配置说明]
            TOOL_INTEGRATION[📄 tool_integration.md<br/>工具集成设计]
            USER_INTERFACE[📄 user_interface.md<br/>用户界面设计]
        end
    end
    
    subgraph "🏗️ 系统架构层次"
        subgraph "用户交互层"
            UI_LAYER[🖥️ 用户界面管理器<br/>CLI/Web/GUI/API]
        end
        
        subgraph "应用控制层"
            APP_LAYER[🎯 分析流程控制器<br/>6步分析流程]
        end
        
        subgraph "Agent协作层"
            AGENT_LAYER[🤖 6个专业化Agent<br/>智能协作机制]
        end
        
        subgraph "核心服务层"
            SERVICE_LAYER[⚙️ 任务/记忆/工具管理<br/>核心业务逻辑]
        end
        
        subgraph "基础设施层"
            INFRA_LAYER[🏛️ 配置/模型/存储<br/>基础设施支撑]
        end
    end
    
    subgraph "🔄 核心流程"
        STEP1[1️⃣ 用户输入任务]
        STEP2[2️⃣ MemoryAgent查找参考]
        STEP3[3️⃣ PlanningAgent制定计划]
        STEP4[4️⃣ 主任务执行循环]
        STEP5[5️⃣ ReportAgent生成报告]
        STEP6[6️⃣ MemoryAgent存储知识]
    end
    
    subgraph "🎯 设计原则"
        PRINCIPLE1[模块化设计<br/>清晰的分层架构]
        PRINCIPLE2[流程驱动<br/>严格的6步分析流程]
        PRINCIPLE3[智能协作<br/>固定调用+智能选择]
        PRINCIPLE4[记忆管理<br/>长短时记忆系统]
        PRINCIPLE5[可扩展性<br/>支持动态扩展]
    end
    
    %% 文档关联
    README --> ARCH
    README --> FLOW
    README --> CONTEXT
    README --> TASK
    README --> COMP
    
    ARCH --> ARCH_DIAGRAM
    FLOW --> FLOW_DIAGRAM
    CONTEXT --> CONTEXT_DIAGRAM
    TASK --> TASK_DIAGRAM
    CONTEXT --> FLOW_CONTEXT
    README --> DESIGN_STRUCTURE
    
    COMP --> AGENT_CONFIG
    COMP --> TOOL_INTEGRATION
    COMP --> USER_INTERFACE
    
    %% 架构层次关联
    ARCH --> UI_LAYER
    ARCH --> APP_LAYER
    ARCH --> AGENT_LAYER
    ARCH --> SERVICE_LAYER
    ARCH --> INFRA_LAYER
    
    %% 流程关联
    FLOW --> STEP1
    STEP1 --> STEP2
    STEP2 --> STEP3
    STEP3 --> STEP4
    STEP4 --> STEP5
    STEP5 --> STEP6
    
    %% 设计原则关联
    ARCH --> PRINCIPLE1
    FLOW --> PRINCIPLE2
    AGENT_LAYER --> PRINCIPLE3
    CONTEXT --> PRINCIPLE4
    COMP --> PRINCIPLE5
    
    %% 样式定义
    classDef docFile fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000000
    classDef diagram fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000000
    classDef implementation fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000000
    classDef architecture fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000000
    classDef process fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000000
    classDef principle fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000000
    
    %% 应用样式
    class README,ARCH,FLOW,CONTEXT,TASK,COMP docFile
    class ARCH_DIAGRAM,FLOW_DIAGRAM,CONTEXT_DIAGRAM,TASK_DIAGRAM,FLOW_CONTEXT,DESIGN_STRUCTURE diagram
    class AGENT_CONFIG,TOOL_INTEGRATION,USER_INTERFACE implementation
    class UI_LAYER,APP_LAYER,AGENT_LAYER,SERVICE_LAYER,INFRA_LAYER architecture
    class STEP1,STEP2,STEP3,STEP4,STEP5,STEP6 process
    class PRINCIPLE1,PRINCIPLE2,PRINCIPLE3,PRINCIPLE4,PRINCIPLE5 principle
