# -*- coding: utf-8 -*-
"""
网络安全Multi-Agent分析系统配置文件
将所有环境配置集中管理，使用Python字典结构
"""

import os

# ====================
# litellm代理配置 - 简化模型管理
# ====================

LITELLM = {
    # litellm代理服务配置
    "base_url": "http://10.5.225.21:18089/v1",  # litellm代理服务地址
    "api_key": "sk-BP_3dbwmFuHI83DcJ-N5XA",  # litellm通常不需要真实API key
    
    # 支持的Chat模型列表
    "chat_models": [
        "deepseek-chat",  # DeepSeek对话模型
        "claude-sonnet-4",  # Claude Sonnet 4对话模型
        "gemini-2.5-flash-preview-05-20",  # Gemini 2.5 Flash快速模型
        "gemini-2.5-pro-preview",  # Gemini 2.5 Pro专业模型
        "secllm-v3",  # 内部安全专用对话模型
        "secllm-v2",
    ],
    
    # 支持的Reasoning模型列表（支持思考过程输出）
    "reasoning_models": [
        "deepseek-reasoner",  # DeepSeek推理模型
        "gemini-2.5-flash-preview-05-20-thinking",  # Gemini 2.5 Flash思考模型
    ],
    
    "select_model": "deepseek-chat",
}

# ====================
# 应用配置
# ====================

APP = {
    # Chainlit配置
    "chainlit": {
        "host": "0.0.0.0",
        "port": 8000,
        "auth_secret": "your-secret-key-here"
    },

    # 系统配置
    "system": {
        "log_level": "INFO",
        "max_agents": 10,
        "analysis_timeout": 3600  # 分析超时时间（秒）
    },

    # 日志配置
    "logging": {
        "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
        "console_level": "INFO",  # 控制台日志级别
        "file_level": "DEBUG",    # 文件日志级别
        "max_file_size": "10MB",  # 单个日志文件最大大小
        "backup_count": 5,        # 保留的日志文件数量
        "format": "%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        "date_format": "%m/%d/%Y %H:%M:%S"
    }
}

# ====================
# 循环控制配置
# ====================

LOOP_CONTROL = {
    # 主流程循环控制
    "max_main_iterations": 20,  # 主任务执行最大循环次数

    # 上下文控制
    "context_compression_threshold": 5000,  # 上下文压缩阈值（字符数）

    # 进度提示格式
    "progress_message_template": "当前{context}，你最多只能分析{max_count}次，现在已经是第{current}次分析"
}

# ====================
# GroupChat配置
# ====================

GROUPCHAT = {
    # GroupChat基本配置
    "max_rounds": 50,  # GroupChat最大对话轮数（子任务协作时使用）
    "allow_repeated_speaker": True,  # 允许同一Agent连续发言

    # 选择器配置
    "selector": {
        "model": "deepseek-chat",  # 用于Agent选择的模型
        "temperature": 0.1,  # 选择的确定性
    }
}

# ====================
# OpenSearch配置
# ====================

OPENSEARCH = {
    # MCP服务器连接配置
    "mcp_server": {
        "url": "http://10.24.45.210:13032/sse/675650f2-5359-4fa5-a407-53b59f83b573",
        "headers": {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"
        },
        "timeout": 30.0,
        "sse_read_timeout": 60.0
    },
}

# ====================
# 配置获取函数
# ====================

def get_config(section=None, key=None, default=None):
    """
    获取配置项，支持环境变量覆盖和默认值
    
    Args:
        section: 配置节名称 (litellm, app, opensearch, groupchat)
        key: 配置键名
        default: 默认值（当section或key不存在时返回）
        
    Returns:
        配置值或配置字典，如果不存在则返回默认值
    """
    config_map = {
        "litellm": LITELLM,
        "app": APP,
        "opensearch": OPENSEARCH,
        "groupchat": GROUPCHAT,
        "loop_control": LOOP_CONTROL
    }
    
    if section is None:
        return config_map
    
    if section not in config_map:
        if default is not None:
            return default
        raise ValueError(f"未知的配置节: {section}")
    
    section_config = config_map[section]
    
    if key is None:
        return section_config
    
    if key not in section_config:
        if default is not None:
            return default
        raise ValueError(f"配置节 {section} 中未找到键: {key}")
    
    return section_config[key]

