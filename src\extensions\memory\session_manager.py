"""
会话管理器 - 负责原始对话记录的存储和管理
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict

from src.extensions.memory.config import memory_config
from src.utils.logger import get_logger


@dataclass
class ConversationMessage:
    """单个对话消息"""
    role: str  # user, assistant, system
    content: str
    timestamp: str
    reasoning: Optional[str] = None  # 推理过程
    tool_call: Optional[Dict[str, Any]] = None  # 工具调用信息
    tool_response: Optional[Dict[str, Any]] = None  # 工具响应信息
    message_summary: Optional[str] = None  # 消息摘要
    metadata: Optional[Dict[str, Any]] = None  # 其他元数据

    # 新增字段：Agent和任务信息
    agent_name: Optional[str] = None  # Agent名称
    agent_role: Optional[str] = None  # Agent角色
    task_type: Optional[str] = None  # 任务类型：main_task, sub_task
    task_id: Optional[str] = None  # 任务ID
    task_title: Optional[str] = None  # 任务标题

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationMessage':
        """从字典创建"""
        return cls(**data)


@dataclass
class Session:
    """会话数据结构"""
    session_id: str
    description: str
    created_at: str
    updated_at: str
    messages: List[ConversationMessage]  # 直接存储消息列表
    overall_summary: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    # 新增字段：任务和Agent信息
    primary_agent: Optional[str] = None  # 主要Agent名称
    task_type: Optional[str] = None  # 会话任务类型：main_task, sub_task
    task_list: Optional[List[Dict[str, Any]]] = None  # 任务清单
    executed_subtasks: Optional[List[Dict[str, Any]]] = None  # 执行的子任务列表
    analysis_plan: Optional[str] = None  # 分析计划
    user_request: Optional[str] = None  # 原始用户请求

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['messages'] = [msg.to_dict() for msg in self.messages]
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """从字典创建"""
        messages = [ConversationMessage.from_dict(msg_data) for msg_data in data['messages']]
        return cls(
            session_id=data['session_id'],
            description=data['description'],
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            messages=messages,
            overall_summary=data.get('overall_summary'),
            metadata=data.get('metadata'),
            # 新增字段
            primary_agent=data.get('primary_agent'),
            task_type=data.get('task_type'),
            task_list=data.get('task_list'),
            executed_subtasks=data.get('executed_subtasks'),
            analysis_plan=data.get('analysis_plan'),
            user_request=data.get('user_request')
        )


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.config = memory_config
        self._sessions_cache: Dict[str, Session] = {}
        self.logger = get_logger("session_manager")
    
    def create_session(self, description: str, metadata: Optional[Dict[str, Any]] = None,
                      primary_agent: Optional[str] = None, task_type: Optional[str] = None,
                      task_list: Optional[List[Dict[str, Any]]] = None,
                      analysis_plan: Optional[str] = None, user_request: Optional[str] = None) -> str:
        """
        创建新会话
        
        Args:
            description: 会话描述
            metadata: 会话元数据
            
        Returns:
            session_id: 会话ID
        """
        session_id = f"sess_{uuid.uuid4().hex[:12]}"
        current_time = datetime.now().isoformat()
        
        session = Session(
            session_id=session_id,
            description=description,
            created_at=current_time,
            updated_at=current_time,
            messages=[],
            overall_summary=None,
            metadata=metadata or {},
            # 新增字段
            primary_agent=primary_agent,
            task_type=task_type,
            task_list=task_list or [],
            executed_subtasks=[],
            analysis_plan=analysis_plan,
            user_request=user_request
        )
        
        # 保存到文件
        self._save_session(session)
        
        # 缓存
        self._sessions_cache[session_id] = session
        
        self.logger.info(f"📝 创建新会话: {session_id} - {description}")
        # print(f"📝 创建新会话: {session_id} - {description}")
        return session_id
    
    def save_conversation(self, session_id: str, messages: List[Dict[str, Any]]) -> bool:
        """
        保存对话到会话

        Args:
            session_id: 会话ID
            messages: 对话消息列表

        Returns:
            bool: 是否保存成功
        """
        try:
            session = self._load_session(session_id)
            if not session:
                self.logger.warning(f"⚠️ 会话不存在: {session_id}")
                # print(f"⚠️ 会话不存在: {session_id}")
                return False

            # 创建对话消息并添加到会话
            for msg_data in messages:
                msg = ConversationMessage(
                    role=msg_data.get('role', ''),
                    content=msg_data.get('content', ''),
                    timestamp=msg_data.get('timestamp', datetime.now().isoformat()),
                    reasoning=msg_data.get('reasoning'),
                    tool_call=msg_data.get('tool_call'),
                    tool_response=msg_data.get('tool_response'),
                    message_summary=msg_data.get('message_summary'),
                    metadata=msg_data.get('metadata'),
                    # 新增字段
                    agent_name=msg_data.get('agent_name'),
                    agent_role=msg_data.get('agent_role'),
                    task_type=msg_data.get('task_type'),
                    task_id=msg_data.get('task_id'),
                    task_title=msg_data.get('task_title')
                )
                session.messages.append(msg)

            session.updated_at = datetime.now().isoformat()

            # 保存
            self._save_session(session)
            self._sessions_cache[session_id] = session

            self.logger.info(f"💾 保存 {len(messages)} 条消息到会话 {session_id}")
            # print(f"💾 保存 {len(messages)} 条消息到会话 {session_id}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 保存对话失败 (session_id: {session_id}): {e}")
            print(f"❌ 保存对话失败 (session_id: {session_id}): {e}")
            return False
    
    def get_messages(self, session_id: str, start_index: int = 0,
                    end_index: Optional[int] = None) -> List[ConversationMessage]:
        """
        获取指定范围的消息（支持单个消息查询）

        Args:
            session_id: 会话ID
            start_index: 起始索引
            end_index: 结束索引，None表示到最后，如果等于start_index+1则查询单个消息

        Returns:
            List[ConversationMessage]: 消息列表
        """
        session = self._load_session(session_id)
        if not session:
            return []

        if end_index is None:
            end_index = len(session.messages)

        return session.messages[start_index:end_index]
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict: 会话信息
        """
        session = self._load_session(session_id)
        if not session:
            return {}
        
        return {
            "session_id": session.session_id,
            "description": session.description,
            "created_at": session.created_at,
            "updated_at": session.updated_at,
            "message_count": len(session.messages),
            "overall_summary": session.overall_summary,
            "metadata": session.metadata
        }
    
    def update_session_summary(self, session_id: str, overall_summary: str) -> bool:
        """
        更新会话整体总结
        
        Args:
            session_id: 会话ID
            overall_summary: 整体总结
            
        Returns:
            bool: 是否更新成功
        """
        try:
            session = self._load_session(session_id)
            if not session:
                return False
            
            session.overall_summary = overall_summary
            session.updated_at = datetime.now().isoformat()
            
            self._save_session(session)
            self._sessions_cache[session_id] = session
            
            self.logger.info(f"📋 更新会话总结: {session_id}")
            # print(f"📋 更新会话总结: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 更新会话总结失败: {e}")
            # print(f"❌ 更新会话总结失败: {e}")
            return False

    def update_session_task_info(self, session_id: str, task_list: Optional[List[Dict[str, Any]]] = None,
                                executed_subtasks: Optional[List[Dict[str, Any]]] = None,
                                analysis_plan: Optional[str] = None) -> bool:
        """
        更新会话的任务信息

        Args:
            session_id: 会话ID
            task_list: 任务清单
            executed_subtasks: 执行的子任务列表
            analysis_plan: 分析计划

        Returns:
            bool: 是否更新成功
        """
        try:
            session = self._load_session(session_id)
            if not session:
                return False

            # 更新任务信息
            if task_list is not None:
                session.task_list = task_list
            if executed_subtasks is not None:
                session.executed_subtasks = executed_subtasks
            if analysis_plan is not None:
                session.analysis_plan = analysis_plan

            session.updated_at = datetime.now().isoformat()

            self._save_session(session)
            self._sessions_cache[session_id] = session

            self.logger.info(f"📋 更新会话任务信息: {session_id}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 更新会话任务信息失败: {e}")
            return False

    def add_executed_subtask(self, session_id: str, subtask_info: Dict[str, Any]) -> bool:
        """
        添加已执行的子任务信息

        Args:
            session_id: 会话ID
            subtask_info: 子任务信息

        Returns:
            bool: 是否添加成功
        """
        try:
            session = self._load_session(session_id)
            if not session:
                return False

            if session.executed_subtasks is None:
                session.executed_subtasks = []

            session.executed_subtasks.append(subtask_info)
            session.updated_at = datetime.now().isoformat()

            self._save_session(session)
            self._sessions_cache[session_id] = session

            self.logger.info(f"📋 添加子任务执行信息: {session_id} - {subtask_info.get('task_title', '未知任务')}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 添加子任务执行信息失败: {e}")
            return False
    
    def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        列出所有会话
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 会话信息列表
        """
        sessions = []
        session_files = list(self.config.sessions_dir.glob("*.json"))
        
        # 按修改时间排序
        session_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        for session_file in session_files[:limit]:
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                sessions.append({
                    "session_id": session_data['session_id'],
                    "description": session_data['description'],
                    "created_at": session_data['created_at'],
                    "updated_at": session_data['updated_at'],
                    "message_count": len(session_data.get('messages', [])),
                    "overall_summary": session_data.get('overall_summary')
                })
                
            except Exception as e:
                self.logger.warning(f"⚠️ 读取会话文件失败 {session_file}: {e}")
                # print(f"⚠️ 读取会话文件失败 {session_file}: {e}")
                continue
        
        return sessions
    
    def _load_session(self, session_id: str) -> Optional[Session]:
        """加载会话"""
        # 先检查缓存
        if session_id in self._sessions_cache:
            return self._sessions_cache[session_id]
        
        # 从文件加载
        session_file = self.config.get_session_file_path(session_id)
        if not session_file.exists():
            return None
        
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            session = Session.from_dict(session_data)
            self._sessions_cache[session_id] = session
            return session
            
        except Exception as e:
            self.logger.error(f"❌ 加载会话失败 {session_id}: {e}")
            # print(f"❌ 加载会话失败 {session_id}: {e}")
            return None
    
    def _save_session(self, session: Session):
        """保存会话到文件"""
        session_file = self.config.get_session_file_path(session.session_id)
        
        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.to_dict(), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"❌ 保存会话失败 {session.session_id}: {e}")
            # print(f"❌ 保存会话失败 {session.session_id}: {e}")
            raise
    
# cleanup_old_sessions 方法已移除
