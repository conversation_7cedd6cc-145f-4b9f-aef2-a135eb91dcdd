

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from src.utils.logger import get_logger
from dataclasses import dataclass, asdict, field


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"           # ⏳ 待处理
    IN_PROGRESS = "in_progress"   # 🔄 处理中
    COMPLETED = "completed"       # ✅ 已完成
    FAILED = "failed"            # ❌ 失败
    CANCELLED = "cancelled"      # 🚫 已取消
    BLOCKED = "blocked"          # ⚠️ 阻塞


class TaskPriority(Enum):
    """任务优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TaskItem:
    """任务项数据结构"""
    task_id: str
    title: str
    description: str
    status: TaskStatus
    priority: TaskPriority = TaskPriority.MEDIUM
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    

    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime序列化
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, (TaskStatus, TaskPriority)):
                data[key] = value.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskItem':
        """从字典创建TaskItem"""
        # 处理datetime反序列化
        for key in ['created_at', 'updated_at', 'completed_at']:
            if data.get(key):
                data[key] = datetime.fromisoformat(data[key])
        
        # 处理枚举反序列化
        if 'status' in data:
            data['status'] = TaskStatus(data['status'])
        if 'priority' in data:
            data['priority'] = TaskPriority(data['priority'])
        
        return cls(**data)



class BaseTaskManager:
    """任务管理器基类"""
    
    def __init__(self, manager_id: str, name: str):
        self.manager_id = manager_id
        self.name = name
        self.tasks: Dict[str, TaskItem] = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def create_task(self, title: str, description: str, priority: TaskPriority = TaskPriority.MEDIUM,
                   assigned_agent: Optional[str] = None, dependencies: Optional[List[str]] = None) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        task = TaskItem(
            task_id=task_id,
            title=title,
            description=description,
            status=TaskStatus.PENDING,
            priority=priority,
            assigned_agent=assigned_agent,
            dependencies=dependencies or []
        )
        
        self.tasks[task_id] = task
        self.updated_at = datetime.now()
        return task_id
    
    def get_task(self, task_id: str) -> Optional[TaskItem]:
        """获取任务"""
        return self.tasks.get(task_id)
    


class AnalysisPhase(Enum):
    """分析阶段枚举"""
    IDLE = "idle"
    MEMORY_SEARCH = "memory_search"  # 步骤2：记忆查找
    PLANNING = "planning"            # 步骤3：计划制定
    MAIN_TASK_EXECUTION = "main_task_execution"  # 步骤4：主任务执行
    SUB_TASK_EXECUTION = "sub_task_execution"    # 步骤4-2：子任务执行
    REPORT_GENERATION = "report_generation"      # 步骤5：报告生成
    KNOWLEDGE_STORAGE = "knowledge_storage"      # 步骤6：知识存储
    COMPLETED = "completed"


class TaskAction(Enum):
    """ReflectionAgent的任务判断结果"""
    CONTINUE_EXECUTION = "continue_execution"    # 继续执行
    TASK_COMPLETED = "task_completed"           # 任务完成
    PLANNING_REQUIRED = "planning_required"     # 需要重新规划
    ALL_TASKS_COMPLETED = "all_tasks_completed" # 所有任务完成


@dataclass
class AnalysisSession:
    """分析会话数据结构"""
    session_id: str
    user_request: str
    current_phase: AnalysisPhase
    memory_context: Optional[str] = None
    analysis_plan: Optional[str] = None
    main_task_manager: Optional[BaseTaskManager] = None
    current_sub_task_manager: Optional[BaseTaskManager] = None
    final_report: Optional[str] = None
    error_message: Optional[str] = None



@dataclass
class AgentExecutionRecord:
    """Agent执行记录 - 记录每次Agent调用的完整信息"""
    agent_name: str
    input_messages: List[TextMessage]
    context_info: Dict[str, Any]

    # 统一消息类型和内容
    message_type: str = "assistant"  # "user", "assistant", "system", "tool"
    reasoning_content: str = ""  # reasoning模型的思考过程

    # 工具调用详情
    tool_calls: List[Dict[str, Any]] = field(default_factory=list)
    tool_results: List[Dict[str, Any]] = field(default_factory=list)

    # 输出和元数据
    output_message: Optional[TextMessage] = None
    execution_metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    execution_duration: float = 0.0

    def get_formatted_summary(self) -> str:
        """获取格式化的执行摘要"""
        summary_parts = [f"Agent: {self.agent_name} ({self.agent_role.value})"]
        summary_parts.append(f"消息类型: {self.message_type}")

        if self.reasoning_content:
            summary_parts.append(f"推理过程: {self.reasoning_content[:100]}...")

        if self.tool_calls:
            summary_parts.append(f"工具调用: {len(self.tool_calls)} 次")
            for tool_call in self.tool_calls:
                summary_parts.append(f"  - {tool_call.get('name', 'Unknown')}: {tool_call.get('status', 'Unknown')}")

        if self.output_message:
            output_content = getattr(self.output_message, 'content', str(self.output_message))
            summary_parts.append(f"输出: {output_content[:100]}...")

        summary_parts.append(f"执行时长: {self.execution_duration:.2f}秒")

        return "\n".join(summary_parts)

@dataclass
class GroupChatContext:
    """GroupChat全局上下文"""
    session_id: str
    task_info: Dict[str, Any]
    messages: List[AgentExecutionRecord] = field(default_factory=list)  # 统一为messages
    conversation_summary: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)




@dataclass
class ConversationMessage:
    """单个对话消息"""
    role: str  # user, assistant, system
    content: str
    timestamp: str
    reasoning: Optional[str] = None  # 推理过程
    tool_call: Optional[Dict[str, Any]] = None  # 工具调用信息
    tool_response: Optional[Dict[str, Any]] = None  # 工具响应信息
    message_summary: Optional[str] = None  # 消息摘要
    metadata: Optional[Dict[str, Any]] = None  # 其他元数据

    # 新增字段：Agent和任务信息
    agent_name: Optional[str] = None  # Agent名称
    agent_role: Optional[str] = None  # Agent角色
    task_type: Optional[str] = None  # 任务类型：main_task, sub_task
    task_id: Optional[str] = None  # 任务ID
    task_title: Optional[str] = None  # 任务标题

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationMessage':
        """从字典创建"""
        return cls(**data)


@dataclass
class Session:
    """会话数据结构"""
    session_id: str
    description: str
    created_at: str
    updated_at: str
    messages: List[ConversationMessage]  # 直接存储消息列表
    overall_summary: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    # 新增字段：任务和Agent信息
    primary_agent: Optional[str] = None  # 主要Agent名称
    task_type: Optional[str] = None  # 会话任务类型：main_task, sub_task
    task_list: Optional[List[Dict[str, Any]]] = None  # 任务清单
    executed_subtasks: Optional[List[Dict[str, Any]]] = None  # 执行的子任务列表
    analysis_plan: Optional[str] = None  # 分析计划
    user_request: Optional[str] = None  # 原始用户请求

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['messages'] = [msg.to_dict() for msg in self.messages]
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """从字典创建"""
        messages = [ConversationMessage.from_dict(msg_data) for msg_data in data['messages']]
        return cls(
            session_id=data['session_id'],
            description=data['description'],
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            messages=messages,
            overall_summary=data.get('overall_summary'),
            metadata=data.get('metadata'),
            # 新增字段
            primary_agent=data.get('primary_agent'),
            task_type=data.get('task_type'),
            task_list=data.get('task_list'),
            executed_subtasks=data.get('executed_subtasks'),
            analysis_plan=data.get('analysis_plan'),
            user_request=data.get('user_request')
        )
