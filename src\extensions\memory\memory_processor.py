"""
记忆处理器 - 负责LLM摘要生成和mem0集成
"""

import os
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.extensions.memory.config import memory_config
from src.utils.logger import get_logger


class CustomLLMClient:
    """自定义LLM客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.base_url = config['base_url']
        self.api_key = config['api_key']
        self.model = config['model']
        self.default_params = {
            'temperature': config.get('temperature', 0.1),
            'max_tokens': config.get('max_tokens', 500),
            'top_p': config.get('top_p', 0.1),
            'repetition_penalty': config.get('repetition_penalty', 1.05)
        }
        self.logger = get_logger("memory_processor")
    
    def generate_summary(self, content: str, instruction: str = "") -> str:
        """
        生成内容摘要
        
        Args:
            content: 要摘要的内容
            instruction: 摘要指令
            
        Returns:
            str: 生成的摘要
        """
        try:
            # 构建提示词
            if instruction:
                prompt = f"{instruction}\n\n内容：\n{content}\n\n请生成简洁的摘要："
            else:
                prompt = f"请为以下对话内容生成简洁的摘要，突出关键信息和要点：\n\n{content}\n\n摘要："
            
            # 构建请求
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': self.model,
                'stream': False,
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                **self.default_params
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                summary = result['choices'][0]['message']['content'].strip()
                return summary
            else:
                self.logger.error(f"❌ LLM请求失败: {response.status_code} - {response.text}")
                # print(f"❌ LLM请求失败: {response.status_code} - {response.text}")
                return self._fallback_summary(content)
                
        except Exception as e:
            self.logger.error(f"❌ LLM摘要生成失败: {e}")
            # print(f"❌ LLM摘要生成失败: {e}")
            return self._fallback_summary(content)
    
    def _fallback_summary(self, content: str, max_length: int = 200) -> str:
        """备用摘要方法"""
        if len(content) <= max_length:
            return content
        
        # 简单截断
        sentences = content.split('。')
        summary = ""
        for sentence in sentences:
            if len(summary + sentence + '。') <= max_length:
                summary += sentence + '。'
            else:
                break
        
        if not summary:
            summary = content[:max_length] + "..."
        
        return summary.strip()


class MemoryProcessor:
    """记忆处理器"""
    
    def __init__(self):
        self.config = memory_config

        # 初始化LLM客户端
        self.llm_client = CustomLLMClient(self.config.llm_config['config'])

        # 初始化mem0 (延迟初始化)
        self._mem0_client = None
        self.logger = get_logger("memory_processor")

        # 记录当前向量数据库配置
        vector_info = self.config.get_current_vector_store_info()
        self.logger.info(f"🗄️ 向量数据库: {vector_info['type']} - {vector_info['description']}")
    
    @property
    def mem0_client(self):
        """延迟初始化mem0客户端"""
        if self._mem0_client is None:
            try:
                # 尝试导入mem0
                from mem0 import Memory
                
                # 创建mem0配置
                mem0_config = self.config.get_mem0_config()

                self._mem0_client = Memory.from_config(mem0_config)
                self.logger.info("✅ mem0客户端初始化成功")
                # print("✅ mem0客户端初始化成功")

            except ImportError as e:
                raise ImportError(f"mem0未安装，请安装mem0: pip install mem0ai") from e
            except Exception as e:
                raise RuntimeError(f"mem0初始化失败: {e}") from e

        return self._mem0_client

    def generate_summary(self, content: str, instruction: str = "") -> str:
        """
        生成内容摘要

        Args:
            content: 要摘要的内容
            instruction: 摘要指令

        Returns:
            str: 生成的摘要
        """
        return self.llm_client.generate_summary(content, instruction)

    def generate_conversation_summary(self, messages: List[Dict[str, Any]]) -> str:
        """
        生成对话轮次摘要

        Args:
            messages: 对话消息列表

        Returns:
            str: 对话摘要
        """
        # 构建对话内容
        conversation_text = ""
        for msg in messages:
            role = msg.get('role', '')
            content = msg.get('content', '')
            if role and content:
                conversation_text += f"{role}: {content}\n"

        if not conversation_text.strip():
            return "空对话"

        # 生成摘要
        instruction = "请为以下对话生成简洁的摘要，突出关键信息、决策和要点："
        summary = self.generate_summary(conversation_text, instruction)

        return summary

    def generate_session_summary(self, session_data: Dict[str, Any]) -> str:
        """
        生成会话整体总结

        Args:
            session_data: 会话数据

        Returns:
            str: 会话总结
        """
        # 收集所有消息摘要
        message_summaries = []
        messages = session_data.get('messages', [])

        for i, msg in enumerate(messages):
            if msg.get('message_summary'):
                role = msg.get('role', 'unknown')
                summary = msg.get('message_summary')
                message_summaries.append(f"消息{i+1}({role}): {summary}")

        if not message_summaries:
            # 如果没有消息摘要，使用消息内容的简要版本
            for i, msg in enumerate(messages[:10]):  # 最多取前10条消息
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')[:100]  # 截取前100字符
                message_summaries.append(f"消息{i+1}({role}): {content}...")

        if not message_summaries:
            return "暂无对话内容"

        # 构建总结内容
        summary_text = f"会话描述: {session_data.get('description', '')}\n\n"
        summary_text += "消息摘要:\n" + "\n".join(message_summaries)

        # 生成整体总结
        instruction = "请为以下会话生成整体总结，概括主要话题、进展和结论："
        overall_summary = self.generate_summary(summary_text, instruction)

        return overall_summary

    def add_to_mem0(self, session_id: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加内容到mem0记忆库

        Args:
            session_id: 会话ID
            content: 内容
            metadata: 元数据

        Returns:
            bool: 是否添加成功
        """
        try:
            # 构建消息格式
            messages = [{"role": "user", "content": content}]

            # 添加到mem0
            result = self.mem0_client.add(
                messages,
                user_id=session_id,  # 使用session_id作为user_id
                metadata=metadata or {}
            )

            self.logger.info(f"✅ 内容已添加到mem0记忆库: {session_id}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 添加到mem0失败: {e}")
            raise RuntimeError(f"mem0记忆存储失败: {e}") from e

    def search_memories(self, query: str, session_id: Optional[str] = None,
                       global_search: bool = True, limit: int = 10) -> List[Dict[str, Any]]:
        """
        搜索相关记忆

        Args:
            query: 查询内容
            session_id: 会话ID，如果指定则优先搜索该会话的记忆
            global_search: 是否进行全局搜索
            limit: 返回结果数量限制

        Returns:
            List[Dict]: 搜索结果
        """
        results = []

        try:
            # 如果指定了session_id，先搜索该会话的记忆
            if session_id:
                session_results = self.mem0_client.search(
                    query,
                    user_id=session_id,
                    limit=limit // 2 if global_search else limit
                )

                # mem0 search返回字典格式: {"results": [...]}
                if isinstance(session_results, dict) and 'results' in session_results:
                    results.extend(session_results['results'])
                elif isinstance(session_results, list):
                    results.extend(session_results)

            # 如果启用全局搜索且还需要更多结果
            if global_search and len(results) < limit:
                remaining_limit = limit - len(results)
                # 全局搜索时使用一个通用的user_id
                global_results = self.mem0_client.search(
                    query,
                    user_id="global_search",
                    limit=remaining_limit
                )

                # mem0 search返回字典格式: {"results": [...]}
                if isinstance(global_results, dict) and 'results' in global_results:
                    results.extend(global_results['results'])
                elif isinstance(global_results, list):
                    results.extend(global_results)

            self.logger.info(f"🔍 搜索到 {len(results)} 条相关记忆")
            # print(f"🔍 搜索到 {len(results)} 条相关记忆")
            return results[:limit]

        except Exception as e:
            raise RuntimeError(f"记忆搜索失败: {e}") from e
