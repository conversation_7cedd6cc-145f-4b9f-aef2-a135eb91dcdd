# 📋 任务管理架构设计

## 概述

任务管理系统采用**双层分层架构**，通过`MainTaskManager`和`SubTaskManager`实现主任务和子任务的分离管理，提供完整的任务生命周期管理、状态跟踪、结果汇总和持久化存储功能。系统与6步分析流程紧密集成，支持任务状态的实时更新和恢复。

## 🏗️ 双层任务管理架构

### 主任务管理器 (MainTaskManager)

#### 实际职责范围
- 管理用户请求和分析会话的完整生命周期
- 存储和更新PlanningAgent制定的分析计划（Markdown格式）
- 管理子任务管理器的创建、执行和销毁
- 汇总所有执行结果和分析结论
- 存储ReportAgent生成的最终分析报告
- 提供任务进度概览和状态查询

#### 实际数据结构
```python
class MainTaskManager(BaseTaskManager):
    # 基础信息
    session_id: str                                    # 分析会话ID
    user_request: str                                  # 用户原始请求
    analysis_plan: Optional[str]                       # PlanningAgent制定的分析计划

    # 子任务管理
    sub_task_managers: Dict[str, SubTaskManager]       # 子任务管理器字典

    # 执行结果
    execution_results: List[Dict[str, Any]]           # 所有执行结果列表
    final_report: Optional[str]                        # 最终分析报告

    # 继承自BaseTaskManager
    manager_id: str                                    # 管理器唯一ID
    name: str                                         # 管理器名称
    tasks: Dict[str, TaskItem]                        # 任务项字典
    created_at: datetime                              # 创建时间
    updated_at: datetime                              # 更新时间
```

#### 核心功能实现
1. **分析计划管理**: `set_analysis_plan()` 存储PlanningAgent的Markdown计划
2. **子任务管理器创建**: `create_sub_task_manager()` 为ReflectionAgent确定的任务创建子管理器
3. **执行结果汇总**: `add_execution_result()` 收集子任务的执行结果
4. **进度跟踪**: `get_task_summary()` 提供任务统计和进度概览
5. **数据持久化**: `export_full_data()` 导出完整数据用于保存和恢复

### 子任务管理器 (SubTaskManager)

#### 实际职责范围
- 管理ReflectionAgent确定的当前执行子任务
- 详细记录ToolExecutionAgent的工具调用过程和结果
- 收集和整理EnhancedGroupChatManager协作过程中的数据
- 存储ThreatAnalysisAgent的分析结论和推理过程
- 提供子任务级别的执行摘要和状态报告

#### 实际数据结构
```python
class SubTaskManager(BaseTaskManager):
    # 关联信息
    main_task_id: str                                  # 主任务ID
    task_title: str                                   # 子任务标题
    parent_session_id: str                            # 父会话ID

    # 执行记录
    tool_executions: List[Dict[str, Any]]             # 工具执行记录列表
    data_collections: List[Dict[str, Any]]            # 数据收集记录列表
    analysis_conclusions: List[Dict[str, Any]]        # 分析结论记录列表

    # 继承自BaseTaskManager
    manager_id: str                                    # 子任务管理器ID
    name: str                                         # 子任务名称
    tasks: Dict[str, TaskItem]                        # 子任务项字典
    created_at: datetime                              # 创建时间
    updated_at: datetime                              # 更新时间
```

#### 核心功能实现
1. **工具执行记录**: `add_tool_execution()` 记录工具名称、参数、结果和状态
2. **数据收集管理**: `add_data_collection()` 整理ToolExecutionAgent收集的数据
3. **分析结论存储**: `add_analysis_conclusion()` 保存ThreatAnalysisAgent的分析结论
4. **执行摘要**: `get_tool_execution_summary()` 生成工具执行的简要摘要
5. **状态查询**: `get_task_summary()` 提供子任务的执行统计

## 📊 任务状态管理（实际实现）

### 任务状态枚举
```python
class TaskStatus(Enum):
    PENDING = "pending"           # ⏳ 待处理 - 任务已创建但未开始
    IN_PROGRESS = "in_progress"   # 🔄 处理中 - 任务正在执行
    COMPLETED = "completed"       # ✅ 已完成 - 任务成功完成
    FAILED = "failed"            # ❌ 失败 - 任务执行失败
    CANCELLED = "cancelled"      # 🚫 已取消 - 任务被用户或系统取消
    BLOCKED = "blocked"          # ⚠️ 阻塞 - 任务因依赖或资源问题被阻塞
```

### 任务优先级枚举
```python
class TaskPriority(Enum):
    LOW = "low"                  # 低优先级 - 可延后处理的任务
    MEDIUM = "medium"            # 中优先级 - 正常优先级任务
    HIGH = "high"               # 高优先级 - 需要优先处理的任务
    CRITICAL = "critical"       # 关键优先级 - 必须立即处理的任务
```

### 状态转换规则（实际实现）
```
PENDING → IN_PROGRESS → COMPLETED    # 正常完成流程
PENDING → IN_PROGRESS → FAILED       # 执行失败流程
PENDING → CANCELLED                  # 直接取消
IN_PROGRESS → BLOCKED → IN_PROGRESS  # 临时阻塞后恢复
IN_PROGRESS → CANCELLED              # 执行中取消
BLOCKED → CANCELLED                  # 阻塞后取消
```

### 状态管理方法
```python
def update_task_status(self, task_id: str, status: TaskStatus,
                      reason: Optional[str] = None) -> bool:
    """更新任务状态"""
    if task_id not in self.tasks:
        return False

    task = self.tasks[task_id]
    old_status = task.status

    # 验证状态转换的合法性
    if self._is_valid_transition(old_status, status):
        task.status = status
        task.updated_at = datetime.now()
        if reason:
            task.metadata["status_change_reason"] = reason
        return True

    return False
```

## 📝 任务项数据结构（实际实现）

### 基础任务项
```python
@dataclass
class TaskItem:
    task_id: str                          # 任务唯一标识符
    title: str                           # 任务标题
    description: str                     # 任务详细描述
    status: TaskStatus                   # 任务当前状态
    priority: TaskPriority               # 任务优先级
    created_at: datetime                 # 任务创建时间
    updated_at: datetime                 # 任务最后更新时间
    completed_at: Optional[datetime]     # 任务完成时间
    assigned_agent: Optional[str]        # 分配的Agent名称
    dependencies: List[str]              # 依赖的任务ID列表
    execution_result: Optional[str]      # 执行结果描述
    error_message: Optional[str]         # 错误信息（如果失败）
    metadata: Dict[str, Any]            # 任务元数据

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于序列化"""
        return {
            "task_id": self.task_id,
            "title": self.title,
            "description": self.description,
            "status": self.status.value,
            "priority": self.priority.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "assigned_agent": self.assigned_agent,
            "dependencies": self.dependencies,
            "execution_result": self.execution_result,
            "error_message": self.error_message,
            "metadata": self.metadata
        }
```

### 任务关系管理（实际实现）
- **层次关系**: MainTaskManager管理SubTaskManager的父子关系
- **会话关联**: 通过session_id关联到分析会话
- **Agent分配**: 任务可以分配给特定的Agent执行
- **依赖跟踪**: 支持任务间的依赖关系定义和检查

## 🔄 任务执行流程（实际实现）

### 主任务创建流程
1. **会话初始化**: `AnalysisFlowController.start_analysis()` 创建分析会话
2. **主任务管理器创建**: 基于用户请求创建MainTaskManager
3. **分析计划制定**: PlanningAgent制定详细分析计划
4. **计划存储**: `set_analysis_plan()` 将计划存储到主任务管理器

### 子任务执行流程
1. **任务确定**: ReflectionAgent确定当前需要执行的子任务
2. **子任务管理器创建**: `create_sub_task_manager()` 创建子任务管理器
3. **Agent协作**: EnhancedGroupChatManager协调ThreatAnalysisAgent和ToolExecutionAgent
4. **执行记录**: 子任务管理器记录工具执行和分析结论
5. **结果汇总**: 子任务完成后将结果汇总到主任务管理器
6. **状态更新**: 更新任务状态和执行结果

### 任务完成流程（实际实现）
1. **结果验证**: ReflectionAgent通过`evaluate_subtask_results()`验证执行结果
2. **质量评估**: 评估任务完成质量并决定下一步行动
3. **状态更新**: 将任务标记为完成或需要继续执行
4. **结果汇总**: `add_execution_result()`将结果存储到主任务管理器
5. **循环控制**: 根据ReflectionAgent的决策继续或结束任务循环

## 🛠️ 工具执行管理（实际实现）

### 工具执行记录
```python
def add_tool_execution(self, tool_name: str, result: str, status: str = "success",
                      parameters: Optional[Dict[str, Any]] = None):
    """记录工具执行过程"""
    execution_entry = {
        "timestamp": datetime.now().isoformat(),
        "tool_name": tool_name,
        "parameters": parameters or {},
        "result": result,
        "status": status  # "success", "failed", "timeout"
    }
    self.tool_executions.append(execution_entry)
    self.updated_at = datetime.now()
```

### 数据收集管理
```python
def add_data_collection(self, data_type: str, source: str, data: Any,
                       metadata: Optional[Dict[str, Any]] = None):
    """记录数据收集过程"""
    collection_entry = {
        "timestamp": datetime.now().isoformat(),
        "data_type": data_type,      # "log_data", "network_data", "file_data"
        "source": source,            # 数据来源
        "data": data,               # 实际数据内容
        "metadata": metadata or {}   # 额外元数据
    }
    self.data_collections.append(collection_entry)
    self.updated_at = datetime.now()
```

### 分析结论管理
```python
def add_analysis_conclusion(self, agent_name: str, conclusion: str,
                           confidence: float = 1.0,
                           metadata: Optional[Dict[str, Any]] = None):
    """记录Agent分析结论"""
    conclusion_entry = {
        "timestamp": datetime.now().isoformat(),
        "agent_name": agent_name,    # 产生结论的Agent名称
        "conclusion": conclusion,    # 分析结论内容
        "confidence": confidence,    # 置信度 (0.0-1.0)
        "metadata": metadata or {}   # 额外信息
    }
    self.analysis_conclusions.append(conclusion_entry)
    self.updated_at = datetime.now()
```

### 执行摘要生成
```python
def get_tool_execution_summary(self) -> str:
    """获取工具执行摘要"""
    if not self.tool_executions:
        return "无工具执行记录"

    summary_parts = []
    for execution in self.tool_executions:
        status_icon = "✅" if execution['status'] == "success" else "❌"
        summary_parts.append(
            f"{status_icon} {execution['tool_name']}: {execution['result'][:100]}..."
        )

    return "\n".join(summary_parts)
```

## 🏭 任务管理器创建（实际实现）

### 直接创建模式
系统采用直接创建模式，无需复杂的工厂类：

```python
# 主任务管理器创建
main_task_manager = MainTaskManager(
    session_id=session.session_id,
    user_request=session.user_request
)

# 子任务管理器创建
sub_task_manager = SubTaskManager(
    main_task_id=current_task_id,
    task_title=task_title,
    parent_session_id=session.session_id
)
```

### 创建时机
- **主任务管理器**: 在`AnalysisFlowController.create_analysis_plan()`中创建
- **子任务管理器**: 在`AnalysisFlowController.determine_current_task()`中创建
- **按需创建**: 只在实际需要时创建，避免资源浪费
- **生命周期绑定**: 与分析会话的生命周期绑定

## 💾 持久化管理（实际实现）

### 存储策略
```python
class TaskManagerPersistence:
    def __init__(self):
        self.storage_dir = Path("./task_storage")
        self.storage_dir.mkdir(exist_ok=True)

    def save_main_task_manager(self, manager: MainTaskManager) -> bool:
        """保存主任务管理器到JSON文件"""
        try:
            file_path = self.storage_dir / f"main_task_{manager.session_id}.json"
            data = manager.export_full_data()

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            print(f"保存主任务管理器失败: {e}")
            return False

    def load_main_task_manager(self, session_id: str) -> Optional[MainTaskManager]:
        """从JSON文件加载主任务管理器"""
        try:
            file_path = self.storage_dir / f"main_task_{session_id}.json"
            if not file_path.exists():
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            return MainTaskManager.from_dict(data)
        except Exception as e:
            print(f"加载主任务管理器失败: {e}")
            return None
```

### 数据格式特性
- **JSON格式**: 人类可读的JSON格式存储
- **UTF-8编码**: 支持中文内容的正确存储
- **结构化数据**: 完整保存任务管理器的所有状态
- **版本兼容**: 通过`export_full_data()`和`from_dict()`方法确保兼容性

### 恢复机制
- **会话恢复**: 支持通过session_id恢复完整的任务管理器状态
- **子任务重建**: 自动重建所有子任务管理器及其状态
- **数据完整性**: 验证加载数据的完整性和一致性
- **错误容错**: 加载失败时返回None，不影响系统运行

## 📊 监控和统计（实际实现）

### 任务统计
```python
def get_task_summary(self) -> Dict[str, Any]:
    """获取任务摘要统计"""
    total = len(self.tasks)
    status_counts = {}

    for status in TaskStatus:
        status_counts[status.value] = len(self.get_tasks_by_status(status))

    return {
        "manager_id": self.manager_id,
        "name": self.name,
        "total_tasks": total,
        "status_counts": status_counts,
        "created_at": self.created_at.isoformat(),
        "updated_at": self.updated_at.isoformat()
    }
```

### 执行统计（子任务管理器）
```python
def get_execution_statistics(self) -> Dict[str, Any]:
    """获取执行统计信息"""
    return {
        "tool_executions": len(self.tool_executions),
        "successful_tools": len([t for t in self.tool_executions if t['status'] == 'success']),
        "failed_tools": len([t for t in self.tool_executions if t['status'] == 'failed']),
        "data_collections": len(self.data_collections),
        "analysis_conclusions": len(self.analysis_conclusions),
        "execution_summary": self.get_tool_execution_summary()
    }
```

### 性能指标
- **任务完成率**: 通过`status_counts`计算完成任务比例
- **工具执行成功率**: 成功工具执行占总执行的比例
- **数据收集效率**: 数据收集记录的数量和质量
- **分析结论产出**: Agent产生的分析结论数量

### 实时监控能力
- **任务状态跟踪**: 通过`updated_at`时间戳跟踪状态变化
- **执行进度监控**: 实时统计工具执行和数据收集进度
- **错误监控**: 记录和统计执行过程中的错误信息
- **性能监控**: 跟踪任务执行时间和资源使用情况

## 🔧 扩展性设计

### 任务类型扩展
- **新任务类型**: 可以通过继承`BaseTaskManager`创建新的任务管理器类型
- **自定义字段**: 通过`metadata`字段支持任务的自定义属性
- **状态扩展**: 可以扩展`TaskStatus`枚举支持新的任务状态

### 存储后端扩展
- **可插拔存储**: 当前使用JSON文件，可以扩展支持数据库存储
- **存储适配器**: 通过适配器模式支持不同的存储后端
- **数据迁移**: 支持不同存储格式间的数据迁移

### 监控扩展
- **自定义指标**: 支持定义和计算自定义的性能指标
- **告警机制**: 可以扩展支持任务执行异常的告警
- **报表生成**: 支持生成详细的任务执行报表
- **执行进度**: 跟踪任务执行进度
- **异常检测**: 检测异常的任务执行情况
- **性能告警**: 性能指标异常时的告警机制

## 扩展性设计

### 插件化任务类型
- **任务类型注册**: 支持新任务类型的注册
- **执行器扩展**: 支持自定义任务执行器
- **状态扩展**: 支持自定义任务状态
- **元数据扩展**: 支持任务元数据的扩展

### 分布式支持
- **任务分发**: 支持任务在多个节点间分发
- **状态同步**: 分布式环境下的状态同步
- **负载均衡**: 任务执行的负载均衡
- **故障转移**: 节点故障时的任务转移

### 集成接口
- **外部系统集成**: 与外部任务管理系统集成
- **API接口**: 提供RESTful API接口
- **事件通知**: 任务状态变化的事件通知
- **数据导出**: 支持任务数据的导出功能
