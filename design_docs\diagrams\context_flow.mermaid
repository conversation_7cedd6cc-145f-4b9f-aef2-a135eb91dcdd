graph LR
    subgraph "记忆工具驱动的上下文生成 (Memory Tool-Driven Context Generation)"
        USER_REQ[👤 用户请求<br/>User Request]
        MEM_TOOLS[🧠 记忆工具调用<br/>Memory Tools Call]
        JSON_SEARCH[📁 本地JSON检索<br/>Local JSON Search]
        VECTOR_SEARCH[🔍 Mem0向量检索<br/>Mem0 Vector Search]
        AGENT_SESSION[🤖 Agent专用会话<br/>Agent-Specific Session]
    end

    subgraph "智能上下文处理 (Intelligent Context Processing)"
        AUTO_SUMMARY[🤖 自动摘要<br/>Auto Summary (LLM)]
        SEMANTIC_MATCH[🔍 语义匹配<br/>Semantic Matching]
        RELEVANCE_SCORE[⭐ 相关性评分<br/>Relevance Scoring]
        LENGTH_CONTROL[📏 长度控制<br/>Length Control]
        FORMAT_CONTEXT[📝 格式化上下文<br/>Format Context]
    end

    subgraph "5个Agent的上下文接收 (5 Agents Context Reception)"
        PLANNING_IN[📋 PlanningAgent<br/>deepseek-reasoner<br/>记忆工具集成]
        REFLECTION_IN[🔍 ReflectionAgent<br/>deepseek-reasoner<br/>记忆工具集成]
        THREAT_IN[🛡️ ThreatAnalysisAgent<br/>deepseek-reasoner<br/>记忆工具集成]
        TOOL_IN[🔧 ToolExecutionAgent<br/>deepseek-chat<br/>记忆工具集成]
        REPORT_IN[📊 ReportAgent<br/>deepseek-chat<br/>记忆工具集成]
    end

    subgraph "自动记忆更新 (Automatic Memory Update)"
        SAVE_INTERACTION[💾 保存Agent交互<br/>save_agent_interaction]
        SESSION_UPDATE[📋 会话状态更新<br/>Session Update]
        VECTOR_STORE[🗄️ 向量记忆存储<br/>Vector Memory Storage]
        CASE_STORAGE[🏛️ 案例存储<br/>store_analysis_case]
    end

    subgraph "混合存储系统 (Hybrid Storage System)"
        JSON_STORAGE[📁 本地JSON存储<br/>Local JSON Storage<br/>./memory_storage/sessions/]
        CHROMA_DB[🧠 ChromaDB向量存储<br/>ChromaDB Vector Storage<br/>./memory_storage/mem0_data/]
        MEM_ADAPTER[🔗 记忆适配器<br/>MemoryIntegrationAdapter]
        SESSION_MGR[📂 会话管理器<br/>SessionManager]
    end

    %% 记忆工具驱动的上下文生成
    USER_REQ --> MEM_TOOLS
    MEM_TOOLS --> JSON_SEARCH
    MEM_TOOLS --> VECTOR_SEARCH
    MEM_TOOLS --> AGENT_SESSION

    %% 智能上下文处理流程
    JSON_SEARCH --> AUTO_SUMMARY
    VECTOR_SEARCH --> SEMANTIC_MATCH
    AGENT_SESSION --> RELEVANCE_SCORE
    AUTO_SUMMARY --> LENGTH_CONTROL
    SEMANTIC_MATCH --> LENGTH_CONTROL
    RELEVANCE_SCORE --> LENGTH_CONTROL
    LENGTH_CONTROL --> FORMAT_CONTEXT

    %% 上下文传递到5个Agent
    FORMAT_CONTEXT --> PLANNING_IN
    FORMAT_CONTEXT --> REFLECTION_IN
    FORMAT_CONTEXT --> THREAT_IN
    FORMAT_CONTEXT --> TOOL_IN
    FORMAT_CONTEXT --> REPORT_IN

    %% Agent工具调用
    PLANNING_IN --> MEM_TOOLS
    REFLECTION_IN --> MEM_TOOLS
    THREAT_IN --> MEM_TOOLS
    TOOL_IN --> MEM_TOOLS
    REPORT_IN --> MEM_TOOLS

    %% 自动记忆更新
    MEM_TOOLS --> SAVE_INTERACTION
    SAVE_INTERACTION --> SESSION_UPDATE
    SESSION_UPDATE --> VECTOR_STORE
    VECTOR_STORE --> CASE_STORAGE

    %% 混合存储系统连接
    SAVE_INTERACTION --> MEM_ADAPTER
    MEM_ADAPTER --> SESSION_MGR
    MEM_ADAPTER --> JSON_STORAGE
    VECTOR_STORE --> CHROMA_DB

    %% 反馈循环
    JSON_STORAGE --> JSON_SEARCH
    CHROMA_DB --> VECTOR_SEARCH
    SESSION_MGR --> AGENT_SESSION

    %% 样式定义
    classDef memoryTools fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000000
    classDef processing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000000
    classDef agents fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000000
    classDef update fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000000
    classDef storage fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000000
    classDef reasoningAgent fill:#ffecb3,stroke:#f57f17,stroke-width:3px,color:#000000
    classDef chatAgent fill:#c8e6c9,stroke:#388e3c,stroke-width:3px,color:#000000

    %% 应用样式
    class USER_REQ,MEM_TOOLS,JSON_SEARCH,VECTOR_SEARCH,AGENT_SESSION memoryTools
    class AUTO_SUMMARY,SEMANTIC_MATCH,RELEVANCE_SCORE,LENGTH_CONTROL,FORMAT_CONTEXT processing
    class PLANNING_IN,REFLECTION_IN,THREAT_IN reasoningAgent
    class TOOL_IN,REPORT_IN chatAgent
    class SAVE_INTERACTION,SESSION_UPDATE,VECTOR_STORE,CASE_STORAGE update
    class JSON_STORAGE,CHROMA_DB,MEM_ADAPTER,SESSION_MGR storage
