"""
分析流程控制器

严格按照README中定义的6步分析流程控制Agent交互：
1. 用户输入任务/指令
2. 记忆管理工具从长期记忆中查找相关参考案例、模板、方案等
3. PlanningAgent制定分析计划和任务清单，创建主任务管理器
4. 主任务执行循环（包含子任务执行循环）
5. ReportAgent生成分析报告
6. 记忆管理工具存储分析报告和相关知识到长期记忆
"""

import asyncio
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from enum import Enum
from dataclasses import dataclass
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken
from src.utils.logger import get_logger

from src.core.user_proxies import BaseUserProxy
from src.core.enhanced_groupchat_manager import EnhancedGroupChatManager, AgentRole
from src.core.task_manager import MainTaskManager, SubTaskManager, TaskItem
from src.tools import get_memory_tool_functions, get_all_tool_functions, get_information_collection_tool_functions, get_execution_tool_functions, get_threat_intelligence_tool_functions
from src.core.context_provider import SingleTurnContextProvider


class AnalysisPhase(Enum):
    """分析阶段枚举"""
    IDLE = "idle"
    MEMORY_SEARCH = "memory_search"  # 步骤2：记忆查找
    PLANNING = "planning"            # 步骤3：计划制定
    MAIN_TASK_EXECUTION = "main_task_execution"  # 步骤4：主任务执行
    SUB_TASK_EXECUTION = "sub_task_execution"    # 步骤4-2：子任务执行
    REPORT_GENERATION = "report_generation"      # 步骤5：报告生成
    KNOWLEDGE_STORAGE = "knowledge_storage"      # 步骤6：知识存储
    COMPLETED = "completed"


class TaskAction(Enum):
    """ReflectionAgent的任务判断结果"""
    CONTINUE_EXECUTION = "continue_execution"    # 继续执行
    TASK_COMPLETED = "task_completed"           # 任务完成
    PLANNING_REQUIRED = "planning_required"     # 需要重新规划
    ALL_TASKS_COMPLETED = "all_tasks_completed" # 所有任务完成


@dataclass
class AnalysisSession:
    """分析会话数据结构"""
    session_id: str
    user_request: str
    current_phase: AnalysisPhase
    memory_context: Optional[str] = None
    analysis_plan: Optional[str] = None
    main_task_manager: Optional[MainTaskManager] = None
    current_sub_task_manager: Optional[SubTaskManager] = None
    final_report: Optional[str] = None
    error_message: Optional[str] = None


class AnalysisFlowController:
    """分析流程控制器 - 严格按照README定义的6步流程执行"""
    
    def __init__(self, user_proxy: BaseUserProxy):
        self.user_proxy = user_proxy
        self.current_session: Optional[AnalysisSession] = None
        self.session_counter = 0
        self.logger = get_logger("analysis_flow")

        # 1. 初始化核心服务
        # memory_manager已移除，现在使用新的记忆管理工具系统
        self.context_provider = SingleTurnContextProvider()

        # 2. 创建增强版GroupChat管理器（专注于子任务执行）
        self.enhanced_groupchat = EnhancedGroupChatManager(
            user_proxy=self.user_proxy,
            context_provider=self.context_provider
        )

        # 3. 创建流程级别的Agent
        self.flow_level_agents = []
        self._create_flow_level_agents()

        # 4. 缓存Agent引用以提高性能
        self._agent_cache = {}
        self._initialize_agent_cache()

    def _create_flow_level_agents(self):
        """创建流程级别的Agent"""
        self.logger.info("🤖 正在创建流程级别的Agent...")
        # print("🤖 正在创建流程级别的Agent...")

        try:
            # 导入Agent实现类
            from src.core.base_managed_agent import BaseManagedAgent

            # 导入Agent配置
            from src.agent_settings.planning_agent import AGENT_CONFIG as planning_config
            from src.agent_settings.report_agent import AGENT_CONFIG as report_config
            from src.agent_settings.reflection_agent import AGENT_CONFIG as reflection_config

            # 流程级Agent配置映射：(实现类, 配置, 角色)
            flow_agent_configs = {
                'PlanningAgent': (BaseManagedAgent, planning_config, AgentRole.PLANNING),
                'ReportAgent': (BaseManagedAgent, report_config, AgentRole.REPORT),
                'ReflectionAgent': (BaseManagedAgent, reflection_config, AgentRole.REFLECTION),
            }

            for agent_name, (agent_class, config, role) in flow_agent_configs.items():
                try:
                    agent = self._create_agent_with_config(agent_name, agent_class, config, role)
                    if agent:
                        self.flow_level_agents.append(agent)
                        self.logger.info(f"   ✅ 创建 {config.get('display_name', agent_name)} 成功 (角色: {role.value})")
                        # print(f"   ✅ 创建 {config.get('display_name', agent_name)} 成功 (角色: {role.value})")
                    else:
                        self.logger.error(f"   ❌ 创建 {agent_name} 失败")
                        # print(f"   ❌ 创建 {agent_name} 失败")

                except Exception as e:
                    self.logger.error(f"   ❌ 创建 {agent_name} 失败: {e}")
                    # print(f"   ❌ 创建 {agent_name} 失败: {e}")
                    continue

            self.logger.info(f"🤖 流程级Agent创建完成，共创建 {len(self.flow_level_agents)} 个Agent")
            # print(f"🤖 流程级Agent创建完成，共创建 {len(self.flow_level_agents)} 个Agent")

        except Exception as e:
            self.logger.error(f"❌ 创建流程级Agent失败: {e}")
            # print(f"❌ 创建流程级Agent失败: {e}")
            self.flow_level_agents = []

    def _create_agent_with_config(self, agent_name: str, agent_class, config: dict, agent_role: AgentRole):
        """使用配置创建Agent实例"""
        try:
            # 获取模型配置
            model_name = config.get("model_name")
            if not model_name:
                self.logger.warning(f"⚠️ {agent_name} 未配置model_name，跳过")
                # print(f"⚠️ {agent_name} 未配置model_name，跳过")
                return None

            # 创建模型客户端
            from autogen_core.models import ModelInfo
            from autogen_ext.models.openai import OpenAIChatCompletionClient
            from src.config import get_config

            litellm_config = get_config("litellm")

            model_info = ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="unknown",
                structured_output=True,
                multiple_system_messages=True,
            )

            model_client = OpenAIChatCompletionClient(
                model=model_name,
                api_key=litellm_config["api_key"],
                base_url=litellm_config["base_url"],
                model_info=model_info
            )

            # 为流程级Agent加载记忆管理工具
            agent_tools = []  # get_memory_tool_functions()
            if agent_tools:
                self.logger.info(f"     ✅ 成功为流程级Agent {agent_name} 加载了 {len(agent_tools)} 个记忆管理工具")
                # print(f"     ✅ 成功为流程级Agent {agent_name} 加载了 {len(agent_tools)} 个记忆管理工具")
            else:
                self.logger.warning(f"     ⚠️ 未找到为流程级Agent {agent_name} 加载的记忆管理工具")
                # print(f"     ⚠️ 未找到为流程级Agent {agent_name} 加载的记忆管理工具")

            # 创建Agent实例 - 添加tools参数
            agent = agent_class(
                name=config["name"],
                system_message=config.get("system_message", ""),
                model_client=model_client,
                context_provider=self.context_provider,
                agent_role=agent_role,
                tools=agent_tools
            )

            return agent

        except Exception as e:
            self.logger.error(f"❌ 创建Agent {agent_name} 时出错: {e}")
            # print(f"❌ 创建Agent {agent_name} 时出错: {e}")
            return None

    def _initialize_agent_cache(self):
        """初始化Agent缓存"""
        # 添加子任务执行Agent (agents是字典，需要遍历值)
        for agent in self.enhanced_groupchat.agents.values():
            self._agent_cache[agent.name] = agent

        # 添加流程级Agent
        for agent in self.flow_level_agents:
            self._agent_cache[agent.name] = agent
    
    def get_agent(self, agent_name: str):
        """获取指定的Agent"""
        agent = self._agent_cache.get(agent_name)
        if not agent:
            raise ValueError(f"未找到Agent: {agent_name}")
        return agent
    
    async def start_analysis(self, user_request: str) -> Tuple[bool, Optional[str]]:
        """
        开始新的分析流程
        返回: (成功标志, 错误信息)
        """
        try:
            # 创建新的分析会话
            self.session_counter += 1
            session_id = f"analysis_session_{self.session_counter}"
            
            self.current_session = AnalysisSession(
                session_id=session_id,
                user_request=user_request,
                current_phase=AnalysisPhase.MEMORY_SEARCH
            )

            # 立即创建主任务管理器
            self.current_session.main_task_manager = MainTaskManager(
                session_id=session_id,
                user_request=user_request
            )
            
            self.logger.info(f"🚀 开始分析会话: {session_id}")
            self.logger.info(f"📝 用户请求: {user_request}")
            # print(f"🚀 开始分析会话: {session_id}")
            # print(f"📝 用户请求: {user_request}")

            # 执行完整的6步分析流程
            success = await self._execute_analysis_flow()

            if success:
                self.logger.info(f"✅ 分析会话 {session_id} 完成")
                # print(f"✅ 分析会话 {session_id} 完成")
                return True, None
            else:
                error_msg = self.current_session.error_message or "分析流程执行失败"
                self.logger.error(f"❌ 分析会话 {session_id} 失败: {error_msg}")
                # print(f"❌ 分析会话 {session_id} 失败: {error_msg}")
                return False, error_msg
                
        except Exception as e:
            error_msg = f"分析流程启动失败: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            # print(f"❌ {error_msg}")
            if self.current_session:
                self.current_session.error_message = error_msg
            return False, error_msg
    
    async def _execute_analysis_flow(self) -> bool:
        """执行完整的6步分析流程"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 分析流程执行错误: 会话未初始化")
            # print("❌ 分析流程执行错误: 会话未初始化")
            return False

        try:
            self.logger.info("🔄 开始执行6步分析流程")

            # 步骤2：MemoryAgent查找相关参考
            self.logger.info("🔍 执行步骤2: MemoryAgent查找相关参考")
            if not await self._step2_memory_search():
                return False

            # 步骤3：PlanningAgent制定计划
            self.logger.info("🎯 执行步骤3: PlanningAgent制定计划")
            if not await self._step3_planning():
                return False

            # 步骤4：主任务执行循环
            self.logger.info("🔄 执行步骤4: 主任务执行循环")
            if not await self._step4_main_task_execution():
                return False

            # 步骤5：ReportAgent生成报告
            self.logger.info("📊 执行步骤5: ReportAgent生成报告")
            if not await self._step5_report_generation():
                return False

            # 步骤6：MemoryAgent存储知识
            self.logger.info("💡 执行步骤6: MemoryAgent存储知识")
            if not await self._step6_knowledge_storage():
                return False

            session.current_phase = AnalysisPhase.COMPLETED
            self.logger.info("✅ 6步分析流程执行完成")
            return True

        except Exception as e:
            session.error_message = f"分析流程执行错误: {str(e)}"
            self.logger.error(f"❌ 分析流程执行错误: {e}")
            # print(f"❌ 分析流程执行错误: {e}")
            return False
    
    async def _step2_memory_search(self) -> bool:
        """步骤2：手动执行记忆查找工具，为PlanningAgent准备上下文"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _step2_memory_search 在没有会话的情况下被调用。")
            return False

        self.logger.info("🧠 步骤2：查找相关参考案例...")
        session.current_phase = AnalysisPhase.MEMORY_SEARCH

        try:
            # 手动执行记忆查找工具，不调用Agent
            from src.tools.memory_tools import search_global_memories

            try:
                # 直接调用记忆搜索工具
                memory_result = await search_global_memories(
                    query=f"与以下用户请求相关的历史案例、模板和方案: {session.user_request}",
                    limit=5
                )

                if memory_result.get("status") == "success" and memory_result.get("memories"):
                    memories = memory_result["memories"]
                    if len(memories) > 0:
                        # 格式化记忆结果
                        formatted_memories = []
                        for memory in memories[:3]:  # 只取前3个最相关的
                            content = memory.get('content', '无内容')
                            formatted_memories.append(f"- {content[:200]}{'...' if len(content) > 200 else ''}")

                        session.memory_context = f"相关历史案例:\n" + "\n".join(formatted_memories)
                        self.logger.info(f"📚 记忆查找完成，找到 {len(memories)} 条相关记忆")
                    else:
                        session.memory_context = "未找到相关的历史案例或模板"
                        self.logger.info("📚 记忆查找完成：未找到相关记忆")
                else:
                    session.memory_context = "未找到相关的历史案例或模板"
                    self.logger.info("📚 记忆查找完成：未找到相关记忆")

            except Exception as tool_error:
                self.logger.warning(f"⚠️ 记忆工具调用失败: {tool_error}")
                session.memory_context = "记忆查找失败，将基于用户请求直接制定计划"

            return True

        except Exception as e:
            # 如果记忆查找失败，继续执行但记录警告
            self.logger.warning(f"⚠️ 记忆查找失败，继续执行: {e}")
            session.memory_context = "记忆查找失败，将基于用户请求直接制定计划"
            return True
    
    async def _step3_planning(self) -> bool:
        """步骤3：PlanningAgent制定分析计划和任务清单"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _step3_planning 在没有会话的情况下被调用。")
            # print("❌ 内部错误: _step3_planning 在没有会话的情况下被调用。")
            return False
        
        self.logger.info("🎯 步骤3：PlanningAgent制定分析计划...")
        # print("🎯 步骤3：PlanningAgent制定分析计划...")
        session.current_phase = AnalysisPhase.PLANNING

        try:
            # 直接调用PlanningAgent（固定Agent）
            planning_agent = self.get_agent("PlanningAgent")

            # 构建计划制定消息 - 简化，上下文由基类注入
            planning_message = f"请为用户请求 '{session.user_request}' 制定详细的分析计划和任务清单。"

            message = TextMessage(content=planning_message.strip(), source="flow_controller")

            response = await planning_agent.on_messages(
                messages=[message],
                cancellation_token=CancellationToken(),
                main_task_manager=session.main_task_manager
            )

            # 提取分析计划
            if hasattr(response, 'chat_message') and response.chat_message:
                session.analysis_plan = getattr(response.chat_message, 'content', str(response.chat_message))
            else:
                raise ValueError("PlanningAgent未返回有效的分析计划")

            self.logger.info("📋 分析计划制定完成")
            # print(f"📋 分析计划制定完成")
            
            self.logger.info(f"📋 分析计划: \n\n{session.analysis_plan}")

            # 更新主任务管理器中的计划
            if session.main_task_manager and session.analysis_plan:
                session.main_task_manager.set_analysis_plan(session.analysis_plan)

            self.logger.info("📋 分析计划已存储")
            # print(f"📋 分析计划已存储")

            return True

        except Exception as e:
            session.error_message = f"计划制定失败: {str(e)}"
            self.logger.error(f"❌ 计划制定失败: {e}")
            # print(f"❌ 计划制定失败: {e}")
            return False

    async def _step4_main_task_execution(self) -> bool:
        """步骤4：主任务执行循环"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _step4_main_task_execution 在没有会话的情况下被调用。")
            # print("❌ 内部错误: _step4_main_task_execution 在没有会话的情况下被调用。")
            return False

        self.logger.info("🔄 步骤4：开始主任务执行循环...")
        # print("🔄 步骤4：开始主任务执行循环...")
        session.current_phase = AnalysisPhase.MAIN_TASK_EXECUTION

        try:
            # 主任务执行阶段不需要GroupChat，使用固定Agent调用

            # 主任务执行循环，直到ReflectionAgent确定任务全部完成
            from src.config import get_config
            loop_config = get_config("loop_control")
            max_main_iterations = loop_config.get("max_main_iterations", 10)  # 从配置读取
            main_iteration = 0

            while main_iteration < max_main_iterations:
                main_iteration += 1
                self.logger.info(f"🔄 主任务循环 - 第{main_iteration}轮")
                # print(f"🔄 主任务循环 - 第{main_iteration}轮")

                # 4-1: ReflectionAgent确定当前需要执行的任务
                current_task = await self._get_current_task_from_reflection()
                if not current_task:
                    self.logger.error("❌ 无法确定当前任务")
                    # print("❌ 无法确定当前任务")
                    return False

                if current_task == "ALL_TASKS_COMPLETED":
                    self.logger.info("✅ 所有主任务已完成")
                    # print("✅ 所有主任务已完成")
                    break

                # 4-2: 执行子任务循环
                sub_task_result = await self._execute_sub_task_loop(current_task)
                if not sub_task_result:
                    self.logger.error(f"❌ 子任务执行失败: {current_task}")
                    # print(f"❌ 子任务执行失败: {current_task}")
                    return False

                # 检查是否需要重新规划
                if sub_task_result == "PLANNING_REQUIRED":
                    self.logger.info("🔄 需要重新规划，更新分析计划...")
                    # print("🔄 需要重新规划，更新分析计划...")
                    if not await self._update_analysis_plan():
                        return False

            if main_iteration >= max_main_iterations:
                self.logger.warning("⚠️ 主任务循环达到最大迭代次数")
                # print("⚠️ 主任务循环达到最大迭代次数")

            return True

        except Exception as e:
            session.error_message = f"主任务执行失败: {str(e)}"
            self.logger.error(f"❌ 主任务执行失败: {e}")
            # print(f"❌ 主任务执行失败: {e}")
            return False

    async def _get_current_task_from_reflection(self) -> Optional[str]:
        """4-1: ReflectionAgent确定当前需要执行的任务"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _get_current_task_from_reflection 在没有会话的情况下被调用。")
            # print("❌ 内部错误: _get_current_task_from_reflection 在没有会话的情况下被调用。")
            return None
        
        try:
            reflection_agent = self.get_agent("ReflectionAgent")

            # 上下文由BaseManagedAgent自动注入，这里只需提供核心指令
            reflection_message = "请根据分析计划和执行历史，确定下一个需要执行的任务。如果所有计划中的任务都已完成，请回复 'ALL_TASKS_COMPLETED'。"
            
            message = TextMessage(content=reflection_message.strip(), source="flow_controller")
            response = await reflection_agent.on_messages(
                messages=[message], 
                cancellation_token=CancellationToken(),
                main_task_manager=session.main_task_manager
                # current_task 在此阶段为None，因为我们在确定下一个任务
            )

            if hasattr(response, 'chat_message') and response.chat_message:
                current_task = getattr(response.chat_message, 'content', str(response.chat_message)).strip()
                self.logger.info(f"🔍 ReflectionAgent确定当前任务: {current_task}")
                # print(f"🔍 ReflectionAgent确定当前任务: {current_task}")
                return current_task
            else:
                self.logger.error("❌ ReflectionAgent未返回有效响应")
                # print("❌ ReflectionAgent未返回有效响应")
                return None

        except Exception as e:
            self.logger.error(f"❌ 获取当前任务失败: {e}")
            # print(f"❌ 获取当前任务失败: {e}")
            return None

    async def _execute_sub_task_loop(self, current_task_desc: str) -> Optional[str]:
        """执行子任务循环"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _execute_sub_task_loop 在没有会话的情况下被调用。")
            # print("❌ 内部错误: _execute_sub_task_loop 在没有会话的情况下被调用。")
            return None
        
        self.logger.info(f"🔧 开始执行子任务: {current_task_desc}")
        # print(f"🔧 开始执行子任务: {current_task_desc}")
        session.current_phase = AnalysisPhase.SUB_TASK_EXECUTION

        try:
            if not session.main_task_manager:
                self.logger.error("❌ 主任务管理器未初始化")
                # print("❌ 主任务管理器未初始化")
                return None
            
            # 为当前任务创建TaskItem和子任务管理器
            main_manager = session.main_task_manager
            task_id = main_manager.create_task(title=current_task_desc, description=current_task_desc)
            current_task_item = main_manager.get_task(task_id)

            sub_manager = main_manager.create_sub_task_manager(
                task_id, current_task_desc
            )
            session.current_sub_task_manager = sub_manager
            
            
            # TODO: 确认 enhanced_groupchat 是否需要 update_subtask_context 方法
            # self.enhanced_groupchat.update_subtask_context(sub_manager)

            # 直接将子任务提交到groupchat，让其内部自动选择合适的agent（通常会先选择ThreatAnalysisAgent）
            collaboration_request = f"请对以下子任务进行分析和处理: {current_task_desc}"

            collaboration_result = await self.enhanced_groupchat.run_subtask_collaboration(
                initial_message=collaboration_request,
                main_task_manager=main_manager,
                current_task=sub_manager
            )

            # 协作完成后，从协作结果和子任务管理器中提取最终结论
            # 优先使用协作结果的对话摘要，其次使用子任务管理器的上下文摘要
            conversation_summary = collaboration_result.get("conversation_summary", "")
            if conversation_summary:
                final_sub_task_conclusion = conversation_summary
            elif sub_manager.context_summary:
                final_sub_task_conclusion = sub_manager.context_summary
            else:
                # 如果都没有，从活动历史生成简要结论
                activity_count = len(sub_manager.activity_history)
                final_sub_task_conclusion = f"子任务 '{current_task_desc}' 协作完成，执行了 {activity_count} 个活动"

            # 4-2-5: ReflectionAgent进行质量评估
            reflection_agent = self.get_agent("ReflectionAgent")
            reflection_request = "请评估上一步的分析结果，并判断下一步行动：'task_completed', 'continue_execution', or 'planning_required'."

            reflection_message = TextMessage(content=reflection_request, source="flow_controller")
            reflection_response = await reflection_agent.on_messages(
                messages=[reflection_message],
                cancellation_token=CancellationToken(),
                main_task_manager=main_manager,
                current_task=current_task_item
            )

            decision = ""
            if hasattr(reflection_response, 'chat_message') and reflection_response.chat_message:
                if hasattr(reflection_response.chat_message, 'content'):
                    decision = reflection_response.chat_message.content.strip().lower()
                else:
                    decision = str(reflection_response.chat_message).strip().lower()
            
            reflection_result = self._parse_reflection_decision(decision)
            
            # 更新短时记忆
            await self._update_short_term_memory(current_task_desc, final_sub_task_conclusion)

            if reflection_result == TaskAction.TASK_COMPLETED.value:
                self.logger.info(f"✅ 子任务协作完成: {current_task_desc}")
                # print(f"✅ 子任务协作完成: {current_task_desc}")
                # 执行结果已经在 _update_short_term_memory 中记录到 MainTaskManager
                return TaskAction.TASK_COMPLETED.value
            elif reflection_result == TaskAction.PLANNING_REQUIRED.value:
                return TaskAction.PLANNING_REQUIRED.value
            else: # Continue
                return TaskAction.CONTINUE_EXECUTION.value

        except Exception as e:
            self.logger.error(f"❌ 子任务执行失败: {e}")
            # print(f"❌ 子任务执行失败: {e}")
            return None

    def _parse_reflection_decision(self, decision: str) -> str:
        """解析ReflectionAgent的决策输出"""
        if "task_completed" in decision or "任务完成" in decision:
            return TaskAction.TASK_COMPLETED.value
        elif "continue_execution" in decision or "继续执行" in decision:
            return TaskAction.CONTINUE_EXECUTION.value
        elif "planning_required" in decision or "重新规划" in decision:
            return TaskAction.PLANNING_REQUIRED.value
        else:
            self.logger.warning(f"⚠️ 无法解析反思决策: {decision}，默认继续执行。")
            # print(f"⚠️ 无法解析反思决策: {decision}，默认继续执行。")
            return TaskAction.CONTINUE_EXECUTION.value

    async def _update_short_term_memory(self, current_task: str, analysis_result: str):
        """手动执行记忆更新工具，更新短时记忆"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _update_short_term_memory 在没有会话的情况下被调用。")
            return

        try:
            # 获取子任务管理器的统一上下文摘要
            context_summary = ""
            if session.current_sub_task_manager:
                # 使用统一的上下文摘要，而不是分别获取工具执行和分析摘要
                context_summary = session.current_sub_task_manager.context_summary
                if not context_summary:
                    # 如果没有上下文摘要，从活动历史生成简要摘要
                    activity_count = len(session.current_sub_task_manager.activity_history)
                    context_summary = f"执行了 {activity_count} 个活动"

            # 手动执行记忆保存工具
            from src.tools.memory_tools import save_agent_interaction

            try:
                # 直接调用记忆保存工具
                await save_agent_interaction(
                    agent_id="system_flow_controller",
                    user_input=current_task,
                    agent_response=analysis_result,
                    reasoning=context_summary,
                    tool_calls=None
                )

                self.logger.info(f"📝 短时记忆更新完成: {current_task}")

            except Exception as tool_error:
                self.logger.warning(f"⚠️ 记忆保存工具调用失败: {tool_error}")

            # 记录执行结果到主任务管理器（简化版本）
            if session.main_task_manager:
                execution_entry = {
                    "task": current_task,
                    "timestamp": datetime.now().isoformat(),
                    "result": analysis_result,
                    "status": "completed",
                    "context_summary": context_summary
                }
                session.main_task_manager.execution_results.append(execution_entry)

        except Exception as e:
            self.logger.warning(f"⚠️ 短时记忆更新失败: {e}")
            # 即使记忆更新失败，也不影响主流程

    async def _update_analysis_plan(self) -> bool:
        """PlanningAgent更新分析计划"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _update_analysis_plan 在没有会话的情况下被调用。")
            # print("❌ 内部错误: _update_analysis_plan 在没有会话的情况下被调用。")
            return False
        
        try:
            planning_agent = self.get_agent("PlanningAgent")

            plan_update_message = f"""
**原始分析计划**:
{session.analysis_plan}

**当前执行结果**:
{self._format_execution_results()}

根据执行反馈，请更新分析计划和任务清单。
"""

            message = TextMessage(content=plan_update_message.strip(), source="flow_controller")
            response = await planning_agent.on_messages(
                messages=[message],
                cancellation_token=CancellationToken(),
                main_task_manager=session.main_task_manager
            )

            if hasattr(response, 'chat_message') and response.chat_message:
                session.analysis_plan = getattr(response.chat_message, 'content', str(response.chat_message))
                self.logger.info("📋 分析计划已更新")
                # print("📋 分析计划已更新")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 计划更新失败: {e}")
            # print(f"❌ 计划更新失败: {e}")
            return False

    async def _step5_report_generation(self) -> bool:
        """步骤5：ReportAgent生成分析报告（固定Agent）"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _step5_report_generation 在没有会话的情况下被调用。")
            # print("❌ 内部错误: _step5_report_generation 在没有会话的情况下被调用。")
            return False
        
        self.logger.info("📊 步骤5：ReportAgent生成分析报告...")
        # print("📊 步骤5：ReportAgent生成分析报告...")
        session.current_phase = AnalysisPhase.REPORT_GENERATION

        try:
            report_agent = self.get_agent("ReportAgent")
            
            report_message_content = "请根据用户请求、最终计划和所有执行历史，撰写一份完整的最终分析报告。"
            message = TextMessage(content=report_message_content, source="flow_controller")

            response = await report_agent.on_messages(
                messages=[message],
                cancellation_token=CancellationToken(),
                main_task_manager=session.main_task_manager
            )

            # 提取最终报告
            if hasattr(response, 'chat_message') and response.chat_message:
                session.final_report = getattr(response.chat_message, 'content', str(response.chat_message))
                if session.main_task_manager and session.final_report:
                    session.main_task_manager.set_final_report(session.final_report)
                self.logger.info("📊 分析报告生成完成")
                # print("📊 分析报告生成完成")
                return True
            else:
                raise ValueError("ReportAgent未返回有效的分析报告")

        except Exception as e:
            session.error_message = f"报告生成失败: {str(e)}"
            self.logger.error(f"❌ 报告生成失败: {e}")
            # print(f"❌ 报告生成失败: {e}")
            return False

    async def _step6_knowledge_storage(self) -> bool:
        """步骤6：手动执行记忆工具，存储分析报告和相关知识到长期记忆"""
        session = self.current_session
        if not session:
            self.logger.error("❌ 内部错误: _step6_knowledge_storage 在没有会话的情况下被调用。")
            return False

        self.logger.info("🧠 步骤6：存储知识到长期记忆...")
        session.current_phase = AnalysisPhase.KNOWLEDGE_STORAGE

        try:
            # 手动执行记忆保存工具
            from src.tools.memory_tools import store_analysis_case

            try:
                # 直接调用分析案例存储工具
                await store_analysis_case(
                    user_request=session.user_request,
                    analysis_plan=session.analysis_plan or "无分析计划",
                    execution_results=self._format_execution_history(),
                    final_report=session.final_report or "无最终报告"
                )

                self.logger.info("🧠 知识存储完成")

            except Exception as tool_error:
                self.logger.warning(f"⚠️ 记忆保存工具调用失败: {tool_error}")

            return True

        except Exception as e:
            self.logger.warning(f"⚠️ 知识存储失败，但不影响主流程: {e}")
            return True

    def get_current_session(self) -> Optional[AnalysisSession]:
        """获取当前分析会话"""
        return self.current_session

    def get_final_report(self) -> Optional[str]:
        """获取最终分析报告"""
        if self.current_session:
            return self.current_session.final_report
        return None

    def is_analysis_completed(self) -> bool:
        """检查分析是否完成"""
        return (self.current_session is not None and
                self.current_session.current_phase == AnalysisPhase.COMPLETED)

    def get_analysis_status(self) -> Dict[str, Any]:
        """获取分析状态信息"""
        session = self.current_session
        if not session:
            return {"status": "no_active_session"}

        return {
            "session_id": session.session_id,
            "current_phase": session.current_phase.value,
            "user_request": session.user_request,
            "has_plan": bool(session.analysis_plan),
            "execution_count": len(session.main_task_manager.execution_results) if session.main_task_manager else 0,
            "has_report": bool(session.final_report),
            "error": session.error_message
        }

    def _format_execution_results(self) -> str:
        """格式化执行结果（使用MainTaskManager）"""
        session = self.current_session
        if not session or not session.main_task_manager or not session.main_task_manager.execution_results:
            return "暂无执行结果"

        formatted_results = []
        for i, result in enumerate(session.main_task_manager.execution_results, 1):
            formatted_results.append(f"{i}. {result.get('task', '未知任务')}: {result.get('status', '未知状态')}")

        return "\n".join(formatted_results)

    def _format_execution_history(self) -> str:
        """格式化执行历史，供ReflectionAgent参考（使用MainTaskManager）"""
        session = self.current_session
        if not session or not session.main_task_manager or not session.main_task_manager.execution_results:
            return "暂无执行历史"

        history_parts = []
        for i, result in enumerate(session.main_task_manager.execution_results, 1):
            task_name = result.get('task', '未知任务')
            task_status = result.get('status', '未知状态')
            task_result = result.get('result', '无结果')

            history_parts.append(f"""
**任务 {i}**: {task_name}
- 状态: {task_status}
- 结果: {task_result[:200]}{'...' if len(task_result) > 200 else ''}
""")

        return "\n".join(history_parts)
