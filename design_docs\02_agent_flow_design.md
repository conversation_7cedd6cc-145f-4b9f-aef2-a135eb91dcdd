# 🔄 Agent流转流程设计

## 概述

系统严格按照README定义的**6步分析流程**执行，通过`AnalysisFlowController`进行流程控制，确保分析过程的规范性和可追溯性。系统已移除MemoryAgent，改用记忆工具集成到各个Agent中，实现了更简化和高效的架构。

## 🎯 实际6步分析流程

### 步骤1: 用户输入任务/指令
- **触发者**: 用户通过命令行界面
- **处理器**: `AnalysisFlowController.start_analysis()`
- **实现**: 创建新的`AnalysisSession`，初始化会话状态
- **输出**: 分析会话创建，状态设为`MEMORY_SEARCH`

### 步骤2: 记忆检索阶段（已重构）
- **执行方式**: 记忆管理系统自动检索（无需专门的MemoryAgent）
- **实现**: `AnalysisFlowController.search_memory_context()`
- **处理**: 通过记忆工具集从Mem0和本地存储中检索相关案例、模板、方案
- **输出**: 记忆上下文字符串，状态更新为`PLANNING`

### 步骤3: PlanningAgent制定分析计划
- **执行者**: `PlanningAgent` (固定调用，使用deepseek-reasoner)
- **实现**: `AnalysisFlowController.create_analysis_plan()`
- **输入**: 用户请求 + 记忆上下文 + Agent自身的记忆工具
- **处理**: 制定详细的分析计划和**最小粒度**任务清单
- **输出**: Markdown格式分析计划，创建`MainTaskManager`，状态更新为`MAIN_TASK_EXECUTION`

### 步骤4: 主任务执行循环
这是最复杂的步骤，通过`AnalysisFlowController.execute_main_tasks()`实现：

#### 4-1: ReflectionAgent确定当前任务
- **执行者**: `ReflectionAgent` (固定调用，使用deepseek-reasoner)
- **实现**: `AnalysisFlowController.determine_current_task()`
- **输入**: 完整分析计划 + 执行历史 + Agent记忆工具
- **处理**: 确定下一个需要执行的任务，创建`SubTaskManager`
- **输出**: 当前任务描述 或 `TaskAction.ALL_TASKS_COMPLETED`

#### 4-2: 子任务执行循环
**唯一使用智能Agent选择的阶段**，通过`EnhancedGroupChatManager.run_subtask_collaboration()`实现：

**4-2-1到4-2-4: 智能Agent协作**
- **协作管理器**: `EnhancedGroupChatManager` (完全自主的Agent选择)
- **参与Agent**: `ThreatAnalysisAgent` ↔ `ToolExecutionAgent`
- **选择机制**: 基于LLM的智能Agent选择，根据对话历史和任务需求
- **协作流程**:
  1. **ThreatAnalysisAgent**分析子任务，制定技术分析方案
  2. **ToolExecutionAgent**执行具体安全工具（OpenSearch查询等）
  3. **ThreatAnalysisAgent**汇总工具结果，生成分析结论
  4. 循环协作直到子任务完成

**4-2-5: ReflectionAgent质量评估**
- **执行者**: `ReflectionAgent` (固定调用)
- **实现**: `AnalysisFlowController.evaluate_subtask_results()`
- **输入**: 当前任务 + 协作结果 + Agent记忆工具
- **处理**: 质量评估和结果验证，决定下一步行动
- **输出**: `TaskAction`枚举值（继续执行/任务完成/重新规划/全部完成）

**4-2-6: 记忆更新（已工具化）**
- **实现方式**: 各Agent通过记忆工具自动更新上下文
- **处理**: 更新Agent专用会话记忆，同步任务状态
- **工具**: `save_agent_interaction`, `get_agent_context`等记忆工具

#### 4-3到4-5: 主任务循环控制
- **4-3**: 如果任务完成，回到4-1选择下一个任务
- **4-4**: 如果需要重新规划，调用PlanningAgent更新计划
- **4-5**: 如果所有任务完成，退出主任务循环，状态更新为`REPORT_GENERATION`

### 步骤5: ReportAgent生成报告
- **执行者**: `ReportAgent` (固定调用，使用deepseek-chat)
- **实现**: `AnalysisFlowController.generate_final_report()`
- **输入**: 用户请求 + 分析计划 + 执行历史 + 执行结果 + Agent记忆工具
- **处理**: 撰写完整的专业安全分析报告，包含影响评估和响应建议
- **输出**: 最终分析报告，状态更新为`KNOWLEDGE_STORAGE`

### 步骤6: 知识存储阶段（已重构）
- **实现方式**: 通过记忆工具自动存储（无需专门的MemoryAgent）
- **实现**: `AnalysisFlowController.store_analysis_knowledge()`
- **处理**: 将分析报告和关键知识存储到Mem0长期记忆，建立知识关联
- **工具**: `store_analysis_case`等记忆工具
- **输出**: 知识存储确认，状态更新为`COMPLETED`

## 🤖 Agent调用模式（实际实现）

### 固定调用模式（流程级Agent）
大部分流程步骤使用固定Agent调用，由`AnalysisFlowController`直接管理：
- **步骤2**: 记忆管理系统（工具化，无专门Agent）
- **步骤3**: `PlanningAgent` (deepseek-reasoner)
- **步骤4-1**: `ReflectionAgent` (deepseek-reasoner)
- **步骤4-2-5**: `ReflectionAgent` (deepseek-reasoner)
- **步骤5**: `ReportAgent` (deepseek-chat)
- **步骤6**: 记忆管理系统（工具化，无专门Agent）

### 智能选择模式（子任务级Agent）
**仅在子任务执行阶段（4-2-1到4-2-4）使用**：
- **管理器**: `EnhancedGroupChatManager`
- **参与Agent**: `ThreatAnalysisAgent` (deepseek-reasoner) ↔ `ToolExecutionAgent` (deepseek-chat)
- **选择机制**: 基于LLM的智能Agent选择器，分析对话历史和任务需求
- **选择原则**:
  - 需要安全分析、策略制定时选择`ThreatAnalysisAgent`
  - 需要运行具体工具时选择`ToolExecutionAgent`
  - 避免同一Agent连续执行过多轮次

## 🎛️ 流程控制机制（实际实现）

### 会话管理
```python
@dataclass
class AnalysisSession:
    session_id: str                                    # 会话唯一标识
    user_request: str                                  # 用户原始请求
    current_phase: AnalysisPhase                       # 当前执行阶段
    memory_context: Optional[str]                      # 记忆检索上下文
    analysis_plan: Optional[str]                       # PlanningAgent制定的分析计划
    main_task_manager: Optional[MainTaskManager]       # 主任务管理器实例
    current_sub_task_manager: Optional[SubTaskManager] # 当前子任务管理器
    execution_results: List[Dict[str, Any]]           # 执行结果历史
    final_report: Optional[str]                        # ReportAgent生成的最终报告
    error_message: Optional[str]                       # 错误信息
    created_at: datetime                               # 会话创建时间
    updated_at: datetime                               # 最后更新时间
```

### 分析阶段枚举
```python
class AnalysisPhase(Enum):
    IDLE = "idle"                           # 空闲状态
    MEMORY_SEARCH = "memory_search"         # 记忆检索阶段
    PLANNING = "planning"                   # 计划制定阶段
    MAIN_TASK_EXECUTION = "main_task_execution"     # 主任务执行阶段
    SUB_TASK_EXECUTION = "sub_task_execution"       # 子任务执行阶段
    REPORT_GENERATION = "report_generation"         # 报告生成阶段
    KNOWLEDGE_STORAGE = "knowledge_storage"         # 知识存储阶段
    COMPLETED = "completed"                 # 分析完成
    ERROR = "error"                         # 错误状态
```

### 任务行动枚举
```python
class TaskAction(Enum):
    CONTINUE_EXECUTION = "continue_execution"       # 继续执行当前任务
    TASK_COMPLETED = "task_completed"              # 当前任务已完成
    PLANNING_REQUIRED = "planning_required"        # 需要重新规划
    ALL_TASKS_COMPLETED = "all_tasks_completed"    # 所有任务已完成
```

### Agent角色枚举
```python
class AgentRole(Enum):
    PLANNING = "planning"           # 计划制定专家
    REFLECTION = "reflection"       # 反思验证专家
    THREAT_ANALYSIS = "threat_analysis"    # 威胁分析专家
    TOOL_EXECUTION = "tool_execution"      # 工具执行专家
    REPORT = "report"              # 报告生成专家
```

## 🛡️ 错误处理和恢复（实际实现）

### 异常处理策略
1. **Agent调用失败**:
   - 记录详细错误信息到会话状态
   - 尝试重试或优雅降级
   - 更新会话状态为`ERROR`
2. **工具执行失败**:
   - 记录失败信息到`SubTaskManager`
   - 不中断主流程，继续其他工具执行
   - 在分析结论中标注工具执行状态
3. **记忆操作失败**:
   - 警告日志但不影响主流程
   - 使用默认上下文继续执行
4. **计划制定失败**:
   - 中断分析流程
   - 返回详细错误信息给用户

### 恢复机制
1. **会话状态持久化**:
   - 每个阶段完成后自动保存会话状态
   - 任务管理器数据实时持久化
2. **断点续传**:
   - 支持从任意`AnalysisPhase`恢复执行
   - 保留已完成的分析结果和上下文
3. **部分结果保留**:
   - 即使失败也保留已完成的任务和分析结论
   - 支持基于部分结果生成报告

## ⚡ 性能优化（实际实现）

### 异步架构
- **主流程异步化**: 所有Agent调用使用`async/await`
- **工具执行并发**: `ToolExecutionAgent`支持并发工具调用
- **记忆操作异步**: 记忆检索和存储操作异步化

### 缓存策略
- **Agent实例缓存**: `AnalysisFlowController`中缓存Agent实例
- **记忆检索缓存**: 相同查询的记忆结果缓存
- **模型客户端复用**: `OpenAIChatCompletionClient`连接池

### 超时控制
- **Agent调用超时**: 每个Agent调用设置`CancellationToken`
- **工具执行超时**: 工具函数内置超时机制
- **整体流程超时**: 分析会话级别的超时保护

### 资源管理
- **内存优化**: 大型上下文的分页加载
- **连接管理**: LiteLLM代理连接的智能管理
- **存储优化**: 任务数据的增量持久化

## 📊 监控和日志（实际实现）

### 流程监控
- **阶段跟踪**: 每个`AnalysisPhase`的开始和结束时间
- **Agent统计**: Agent调用次数、耗时和成功率统计
- **错误监控**: 错误发生频率、类型和恢复情况

### 详细日志
- **用户交互**: 完整的用户请求和系统响应
- **Agent通信**: Agent间的消息传递和上下文传递
- **工具执行**: 工具调用参数、执行过程和结果
- **记忆操作**: 记忆检索、存储和上下文管理操作
- **错误追踪**: 完整的错误堆栈和上下文信息

### 系统状态
- **会话状态**: 实时的分析会话状态和进度
- **资源使用**: 内存、CPU和网络资源使用情况
- **性能指标**: 响应时间、吞吐量和并发处理能力
