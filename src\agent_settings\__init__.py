"""
Agent配置模块

只包含Agent的配置信息，具体实现在 src/core/agents/ 中
"""

# 导入所有Agent配置
from .planning_agent import AGENT_CONFIG as PLANNING_CONFIG
from .threat_analysis_agent import AGENT_CONFIG as THREAT_ANALYSIS_CONFIG
from .traffic_log_agent import AGENT_CONFIG as TRAFFIC_LOG_CONFIG
from .code_execution_agent import AGENT_CONFIG as CODE_EXECUTION_CONFIG
from .reflection_agent import AGENT_CONFIG as REFLECTION_CONFIG
from .report_agent import AGENT_CONFIG as REPORT_CONFIG

__all__ = [
    # Agent配置
    "PLANNING_CONFIG",
    "THREAT_ANALYSIS_CONFIG",
    "TRAFFIC_LOG_CONFIG",
    "CODE_EXECUTION_CONFIG",
    "REFLECTION_CONFIG",
    "REPORT_CONFIG",
]