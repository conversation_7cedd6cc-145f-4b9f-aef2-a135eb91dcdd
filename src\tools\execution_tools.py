"""
代码和命令执行工具实现

提供Python代码执行和Ubuntu命令执行工具，支持安全的代码执行环境和结果收集。
这些工具被定义为可由AutoGen Agent直接调用的Python函数。
"""

import asyncio
import json
import os
import subprocess
import sys
import tempfile
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from typing_extensions import Annotated
from src.utils.logger import get_logger
import traceback

logger = get_logger("execution_tools")


def _ensure_results_directory():
    """确保结果保存目录存在"""
    results_dir = "data/execution_results"
    os.makedirs(results_dir, exist_ok=True)
    return results_dir


def _save_execution_results(result: Dict[str, Any], tool_name: str) -> str:
    """
    保存执行结果到文件
    
    :param result: 执行结果字典
    :param tool_name: 工具名称
    :return: 保存的文件路径
    """
    results_dir = _ensure_results_directory()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{tool_name}_{timestamp}.json"
    filepath = os.path.join(results_dir, filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        return filepath
    except Exception as e:
        logger.error(f"⚠️ 保存执行结果失败: {str(e)}")
        # print(f"⚠️ 保存执行结果失败: {str(e)}")
        return ""


async def execute_python_code(
    code: Annotated[str, "要执行的Python代码"],
    timeout: Annotated[int, "执行超时时间（秒），默认30秒"] = 30,
    capture_output: Annotated[bool, "是否捕获标准输出和错误输出，默认True"] = True,
    working_directory: Annotated[Optional[str], "工作目录，默认为当前目录"] = None,
    save_to_file: Annotated[bool, "是否将结果保存到文件，默认True"] = True
) -> Dict[str, Any]:
    """
    执行Python代码并返回结果
    
    :param code: 要执行的Python代码
    :param timeout: 执行超时时间（秒），默认30秒
    :param capture_output: 是否捕获标准输出和错误输出，默认True
    :param working_directory: 工作目录，默认为当前目录
    :param save_to_file: 是否将结果保存到文件，默认True
    :return: 包含执行结果的字典
    """
    logger.info(f"🐍 [Tool] 开始执行Python代码 (超时: {timeout}s)")
    # print(f"🐍 [Tool] 开始执行Python代码 (超时: {timeout}s)")
    
    start_time = time.time()
    result = {
        "tool_name": "execute_python_code",
        "code": code,
        "timeout": timeout,
        "working_directory": working_directory or os.getcwd(),
        "start_time": datetime.now().isoformat(),
        "success": False,
        "stdout": "",
        "stderr": "",
        "return_code": None,
        "execution_time": 0,
        "error_message": ""
    }
    
    try:
        # 创建临时Python文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(code)
            temp_file_path = temp_file.name
        
        try:
            # 设置工作目录
            cwd = working_directory or os.getcwd()
            
            # 执行Python代码
            if capture_output:
                process = await asyncio.create_subprocess_exec(
                    sys.executable, temp_file_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=cwd
                )
                
                try:
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(), 
                        timeout=timeout
                    )
                    
                    result["stdout"] = stdout.decode('utf-8', errors='replace')
                    result["stderr"] = stderr.decode('utf-8', errors='replace')
                    result["return_code"] = process.returncode
                    result["success"] = process.returncode == 0
                    
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    result["error_message"] = f"执行超时 ({timeout}秒)"
                    result["return_code"] = -1
                    
            else:
                # 不捕获输出，直接执行
                process = await asyncio.create_subprocess_exec(
                    sys.executable, temp_file_path,
                    cwd=cwd
                )
                
                try:
                    result["return_code"] = await asyncio.wait_for(
                        process.wait(), 
                        timeout=timeout
                    )
                    result["success"] = result["return_code"] == 0
                    
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    result["error_message"] = f"执行超时 ({timeout}秒)"
                    result["return_code"] = -1
                    
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
                
    except Exception as e:
        result["error_message"] = f"执行失败: {str(e)}"
        result["stderr"] = traceback.format_exc()
        
    # 计算执行时间
    result["execution_time"] = round(time.time() - start_time, 3)
    result["end_time"] = datetime.now().isoformat()
    
    # 保存结果到文件
    if save_to_file:
        filepath = _save_execution_results(result, "python_execution")
        result["saved_to"] = filepath
    
    # 输出执行状态
    if result["success"]:
        logger.info(f"Python代码执行成功 (耗时: {result['execution_time']}s)")
        # print(f"✅ [Tool] Python代码执行成功 (耗时: {result['execution_time']}s)")
    else:
        logger.error(f"Python代码执行失败: {result['error_message']}")
        # print(f"❌ [Tool] Python代码执行失败: {result['error_message']}")

    return result


async def execute_ubuntu_command(
    command: Annotated[str, "要执行的Ubuntu命令"],
    timeout: Annotated[int, "执行超时时间（秒），默认60秒"] = 60,
    shell: Annotated[bool, "是否使用shell执行，默认True"] = True,
    working_directory: Annotated[Optional[str], "工作目录，默认为当前目录"] = None,
    environment: Annotated[Optional[Dict[str, str]], "环境变量字典"] = None,
    save_to_file: Annotated[bool, "是否将结果保存到文件，默认True"] = True
) -> Dict[str, Any]:
    """
    执行Ubuntu系统命令并返回结果
    
    :param command: 要执行的Ubuntu命令
    :param timeout: 执行超时时间（秒），默认60秒
    :param shell: 是否使用shell执行，默认True
    :param working_directory: 工作目录，默认为当前目录
    :param environment: 环境变量字典
    :param save_to_file: 是否将结果保存到文件，默认True
    :return: 包含执行结果的字典
    """
    logger.info(f"🖥️ [Tool] 开始执行Ubuntu命令: {command[:100]}...")
    # print(f"🖥️ [Tool] 开始执行Ubuntu命令: {command[:100]}...")
    
    start_time = time.time()
    result = {
        "tool_name": "execute_ubuntu_command",
        "command": command,
        "timeout": timeout,
        "shell": shell,
        "working_directory": working_directory or os.getcwd(),
        "start_time": datetime.now().isoformat(),
        "success": False,
        "stdout": "",
        "stderr": "",
        "return_code": None,
        "execution_time": 0,
        "error_message": ""
    }
    
    try:
        # 设置工作目录和环境变量
        cwd = working_directory or os.getcwd()
        env = os.environ.copy()
        if environment:
            env.update(environment)
        
        # 执行命令
        if shell:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd,
                env=env
            )
        else:
            # 分割命令参数
            cmd_args = command.split()
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd,
                env=env
            )
        
        try:
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=timeout
            )
            
            result["stdout"] = stdout.decode('utf-8', errors='replace')
            result["stderr"] = stderr.decode('utf-8', errors='replace')
            result["return_code"] = process.returncode
            result["success"] = process.returncode == 0
            
        except asyncio.TimeoutError:
            process.kill()
            await process.wait()
            result["error_message"] = f"命令执行超时 ({timeout}秒)"
            result["return_code"] = -1
            
    except Exception as e:
        result["error_message"] = f"命令执行失败: {str(e)}"
        result["stderr"] = traceback.format_exc()
        
    # 计算执行时间
    result["execution_time"] = round(time.time() - start_time, 3)
    result["end_time"] = datetime.now().isoformat()
    
    # 保存结果到文件
    if save_to_file:
        filepath = _save_execution_results(result, "ubuntu_command")
        result["saved_to"] = filepath
    
    # 输出执行状态
    if result["success"]:
        logger.info(f"✅ [Tool] Ubuntu命令执行成功 (耗时: {result['execution_time']}s)")
        # print(f"✅ [Tool] Ubuntu命令执行成功 (耗时: {result['execution_time']}s)")
        if result["stdout"]:
            logger.info(f"📄 输出: {result['stdout'][:200]}...")
            # print(f"📄 输出: {result['stdout'][:200]}...")
    else:
        logger.error(f"❌ [Tool] Ubuntu命令执行失败: {result['error_message']}")
        # print(f"❌ [Tool] Ubuntu命令执行失败: {result['error_message']}")
        if result["stderr"]:
            logger.error(f"🚨 错误: {result['stderr'][:200]}...")
            # print(f"🚨 错误: {result['stderr'][:200]}...")
            
    return result


def get_execution_tool_functions() -> List[Callable]:
    """
    获取所有代码和命令执行工具函数
    
    :return: 包含所有工具函数的列表
    """
    return [
        execute_python_code,
        execute_ubuntu_command
    ]
