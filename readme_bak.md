# 🛡️ 网络安全Multi-Agent智能分析平台


## 🎯 项目概述

这是一个基于**Multi-Agent**的**智能化网络安全分析平台**，用于从威胁检测、溯源、到响应报告的**端到端自动化分析**。

### 🚀 核心特色

- 🧠 **多个专业化Agent**：计划制定Agent、反思验证Agent、记忆管理Agent、威胁分析Agent、工具执行Agent、报告生成Agent
- 🤖 **多模型支持**：支持OpenAI GPT-4、Google Gemini、Anthropic Claude、本地Ollama等多种模型接入
- ⚡ **智能协作机制**：基于AutoGen SelectorGroupChat的LLM智能Agent选择
- 📋 **配置驱动架构**：完全基于配置文件的Agent定义，支持动态扩展
- 📊 **任务管理系统**：完整的任务追踪、进度管理和质量评估
- 🧠 **记忆管理系统**：完善的长短时记忆和Agent上下文管理。

## 🤖 多模型Agent架构

### 简化模型管理
- **基于litellm代理**：所有模型通过统一的OpenAI兼容接口访问
- **模型类型区分**：Chat模型💬（快速响应）和 Reasoning模型🧠（思考过程）
- **简洁配置**：直接在Agent配置中指定model_name，无需复杂管理器
- **多个专用模型**：deepseek-chat/claude-sonnet-4/gemini-2.5系列/secllm-v3等

### 多个专业化Agent

#### 🎯 **PlanningAgent** - 计划制定专家
- **模型类型**: reasoner (deepseek-reasoner, Gemini-2.5-Pro)
- **核心职责**: 计划制定、任务分解、动态计划修改
- **专业能力**: 制定全面、可执行的分析策略和行动计划，将策略分解为**最小粒度**的清晰、可执行的任务清单，根据分析进展和执行反馈动态调整分析策略。

#### ✅ **ReflectionAgent** - 反思验证专家
- **模型类型**: reasoner (deepseek-reasoner, Gemini-2.5-Pro)
- **核心职责**: 任务确定、结果验证、逻辑检查、任务评估和任务总结
- **专业能力**: 依据计划清单，确定当前需要执行的任务。审查子任务分析结论，评估当前子任务的完成度，并更新子任务状态。更新任务清单，并判断下一步的最佳行动。

#### 🧠 **MemoryAgent** - 记忆管理专家
- **模型类型**: chat (Gemini-2.5-Flash, deepseek-chat)
- **核心职责**: 上下文管理、知识管理、历史案例关联、历史模板和方案管理
- **专业能力**: 管理长短时记忆，短时记忆包括对话上下文的管理、摘要、总结、筛选，控制上下文长度并处理不同Agent之间上下文的筛选和传递。长时记忆包括不同任务的解决模板、方案、案例等，通过RAG为 PlanningAgent 和 ReflectionAgent 等Agent处理不同任务时提供指导信息。

#### 🔬 **ThreatAnalysisAgent** - 威胁分析专家  
- **模型类型**: reasoner (deepseek-reasoner, Gemini-2.5-Pro)
- **核心职责**: 深度技术分析、攻击行为识别、网络安全知识、威胁研判、日志分析
- **专业能力**: 接收子任务并处理，首先确定解决思路和方案，并一步步的指导工具执行、情报检索、数据收集等Agent行动，最终汇总信息，给出分析结果。

#### 🛠️ **ToolExecutionAgent** - 工具执行专家
- **模型类型**: chat (Gemini-2.5-Flash, deepseek-chat)
- **核心职责**: 安全工具调用、结果处理、执行监控
- **专业能力**: 通过 工具接口、MCP服务等，接入多种 安全分析、情报检索、数据收集 工具。根据 ThreatAnalysisAgent 的分析结论，调用工具执行，汇总清洗结果然后反馈给 ThreatAnalysisAgent。

#### 📊 **ReportAgent** - 报告生成专家
- **模型类型**: chat (Gemini-2.5-Flash, deepseek-chat)
- **核心职责**: 整合结果、撰写报告、影响评估、响应建议
- **专业能力**: 完成整个分析任务后，总结分析思路、详细的分析过程，撰写分析报告，给出分析结果、影响评估和响应建议。

## 🏗️ 技术架构

### 核心组件

#### 🤖 AgentManager - Agent配置和管理
每个Agent都支持完全自定义，通过配置文件，指定Agent信息，并动态创建。

#### 📊 TaskManager - 任务管理
分为主任务管理器和子任务管理器。

每个用户任务创建一个主任务管理器，管理用户指令、PlanningAgent制定的整体的任务清单、子任务列表、执行结果、上下文记忆、参考模板案例等。

ReflectionAgent 根据主任务创建当前执行的子任务管理器，管理当前执行的子任务的记忆、上下文、工具执行、数据收集等。

#### 🧠 MemoryManager - 记忆管理
针对每个任务管理器创建一个记忆管理器。用于当前任务的长短时记忆管理。

并在适当的时候进行摘要、总结、筛选，控制Agent的上下文长度。通过记忆管理器，控制不同Agent之间上下文的筛选和传递，同步执行信息、任务状态、中间分析结论等。

#### 🤖 GroupChatManager - 多Agent协作管理
控制整体的分析流程、多Agent交互流程。

#### 📚 ToolManager - 工具管理
初始化工具列表，并注册到 ToolExecutionAgent 。

#### 🤖 ModelManager - 模型管理
提供统一的模型交互接口即可，根据上下文，控制流式/非流式返回。

#### 🤖 UserProxyAgent - 用户代理
提供统一的用户干涉接口即可，用于用户确定、请求用户补充信息等场景。
通过统一的接口提供，便于后续更换不同的前端或交互方式，例如命令行、chainlit或其他的web界面等。
当前使用命令行进行初步开发即可。

#### 🤖 UIProxyAgent - 前端界面代理
提供统一的展示接口即可，用于展示信息、交互等场景。
通过统一的接口提供，便于后续更换不同的前端展示方式，例如命令行、chainlit或其他的web界面等。
当前使用命令行进行初步开发即可。

### 分析流程
1. 用户输入任务/指令。
2. MemoryAgent从长期记忆中，查找相关的参考案例、模板、方案等，为PlanningAgent提供参考。
3. PlanningAgent根据用户输入的任务/指令，结合MemoryAgent提供的参考信息，制定分析计划和任务清单，创建主任务管理器。
4. 主任务执行（循环，直到ReflectionAgent确定任务全部完成）：
   4-1. ReflectionAgent根据任务清单，确定当前需要执行的任务，创建子任务管理器。
   4-2. 子任务执行（循环，直到ReflectionAgent确定任务完成）：
       4-2-1. ThreatAnalysisAgent执行当前子任务，进行深入的威胁分析。
       4-2-2. ThreatAnalysisAgent指导ToolExecutionAgent执行具体的安全工具，检索情报，收集数据等。
       4-2-3. ToolExecutionAgent执行具体的安全工具，收集数据。
       4-2-4. ThreatAnalysisAgent汇总工具执行结果，给出分析结论。
       4-2-5. ReflectionAgent进行质量评估和结果验证，判断下一步行动（任务完成、继续执行、planning重新规划、任务全部完成）。
       4-2-6. MemoryAgent更新短时记忆，管理、摘要、总结、筛选当前子任务记忆。并通过记忆管理器，更新主任务和子任务状态，处理不同Agent之间上下文的筛选和传递。
       4-2-7. 如果为继续执行，ThreatAnalysisAgent根据ReflectionAgent的评估结果和建议，进行新的工具执行、数据收集、分析。
       4-2-8.  如果为任务完成或planning重新规划，则退出当前子任务执行循环。
   4-3. 解析ReflectionAgent输出，如果为任务完成，则回到第4.1步，选择下一个需要执行的任务。
   4-4. 解析ReflectionAgent输出，如果为planning重新规划，则由PlanningAgent更新分析计划和任务清单，然后回到第4.1步，选择下一个需要执行的任务。
   4-5. 解析ReflectionAgent输出，如果为任务全部完成，则退出当前主任务执行循环。
5. ReportAgent根据主任务管理器中的任务清单和执行结果，撰写分析报告。
6. MemoryAgent将分析报告和相关知识，存储到长期记忆中，供后续任务参考。

---
本项目灵感来源于多个优秀开源项目：
- [AutoGen](https://github.com/microsoft/autogen) - Multi-Agent协作框架
- [Chainlit](https://github.com/chainlit/chainlit) - 会话式AI应用构建框架
- [Mem0](https://github.com/mem0ai/mem0) - AI记忆管理系统
- [Suna](https://github.com/kortix-ai/suna) - 一个开源的通用AI Agent项目，亮点使用`todo.md`文件作为任务管理。
- [II-Agent](https://github.com/Intelligent-Internet/ii-agent) - 一个开源的智能助手项目，亮点为智能的上下文管理。

---

**让AI为网络安全赋能，构建更安全的数字世界** 🛡️✨
