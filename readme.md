# 🛡️ 网络安全Multi-Agent智能分析平台

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![AutoGen](https://img.shields.io/badge/AutoGen-Latest-green.svg)](https://github.com/microsoft/autogen)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 项目概述

这是一个基于**Microsoft AutoGen**的**智能化网络安全分析平台**，实现从威胁检测、深度分析到响应报告的**端到端自动化分析**。系统采用严格的6步分析流程，通过5个专业化Agent的智能协作，提供专业级的安全事件分析能力。

### 🚀 核心特色

- 🧠 **5个专业化Agent**：计划制定、反思验证、威胁分析、工具执行、报告生成
- 🤖 **多模型支持**：通过LiteLLM代理支持DeepSeek、Claude、Gemini等多种模型
- ⚡ **智能协作机制**：基于AutoGen的自主Agent选择和消息传递
- 📋 **配置驱动架构**：完全基于配置文件的Agent定义，支持动态扩展
- 📊 **分层任务管理**：主任务管理器+子任务管理器的双层任务追踪
- 🧠 **混合记忆架构**：本地JSON存储+Mem0向量记忆的智能记忆管理

## 🏗️ 技术架构

### 🔧 技术栈
- **核心框架**: Microsoft AutoGen (Agent协作框架)
- **模型代理**: LiteLLM (统一多模型接口)
- **记忆管理**: Mem0 + 本地JSON存储 (混合记忆架构)
- **向量数据库**: ChromaDB (本地向量存储)
- **用户界面**: 命令行界面 (支持扩展Web界面)
- **配置管理**: Python配置文件 (集中化配置)

### 🤖 5个专业化Agent

#### 🎯 **PlanningAgent** - 计划制定专家
- **模型**: `deepseek-reasoner` (推理模型)
- **核心职责**: 分析策略制定、任务分解、动态计划调整
- **工具能力**: 记忆管理工具 (创建会话、保存交互、搜索记忆、获取上下文)
- **专业能力**: 制定全面可执行的分析策略，将复杂任务分解为**最小粒度**的可执行清单，根据执行反馈动态调整策略

#### ✅ **ReflectionAgent** - 反思验证专家
- **模型**: `deepseek-reasoner` (推理模型)
- **核心职责**: 任务确定、结果验证、逻辑检查、质量评估
- **工具能力**: 记忆管理工具
- **专业能力**: 依据计划清单确定当前执行任务，审查分析结论，评估任务完成度，更新任务状态，判断下一步行动

#### 🔬 **ThreatAnalysisAgent** - 威胁分析专家
- **模型**: `deepseek-reasoner` (推理模型)
- **核心职责**: 深度技术分析、攻击行为识别、威胁研判
- **工具能力**: 记忆管理工具
- **专业能力**: 接收子任务进行深度分析，制定解决思路，指导工具执行和数据收集，汇总信息给出专业分析结论

#### 🌐 **TrafficLogAgent** - 流量日志分析专家
- **模型**: `deepseek-chat` (对话模型)
- **核心职责**: OpenSearch流量日志检索、查询、过滤、筛选、聚合和分析
- **工具能力**: OpenSearch流量日志查询 + 记忆管理工具
- **专业能力**: 从OpenSearch中精确检索网络流量数据，进行多维度数据过滤和流量模式分析，为威胁分析提供流量情报支撑

#### 💻 **CodeExecutionAgent** - 代码执行专家
- **模型**: `deepseek-chat` (对话模型)
- **核心职责**: Python代码执行和Ubuntu系统命令执行
- **工具能力**: Python代码执行 + Ubuntu命令执行 + 记忆管理工具
- **专业能力**: 安全执行Python代码和Ubuntu命令，处理数据分析、文件操作和系统管理任务，为威胁分析提供计算和系统操作支撑

#### 📊 **ReportAgent** - 报告生成专家
- **模型**: `deepseek-chat` (对话模型)
- **核心职责**: 结果整合、报告撰写、响应建议
- **工具能力**: 记忆管理工具 + 分析案例存储
- **专业能力**: 汇总分析过程和结果，撰写专业安全分析报告，提供影响评估和响应建议，存储分析案例供后续参考

### �️ 核心组件架构

#### 📊 **分层任务管理系统**
- **MainTaskManager**: 主任务管理器，管理用户请求、分析计划、子任务列表、执行结果和最终报告
- **SubTaskManager**: 子任务管理器，管理具体子任务的执行、工具调用、分析结论和状态跟踪
- **任务持久化**: 支持任务状态保存和恢复，确保分析过程的连续性

#### 🧠 **混合记忆管理系统**
- **本地JSON存储**: 原始对话记录的完整保存，支持精确的历史回溯
- **Mem0向量记忆**: 智能记忆摘要和语义检索，支持跨会话的知识关联
- **Agent专用会话**: 每个Agent独立的记忆空间，支持个性化上下文管理
- **记忆工具集**: 标准化的记忆操作接口，替代传统的MemoryAgent

#### 🤖 **增强版GroupChat管理器**
- **自主Agent选择**: 基于LLM的智能Agent选择，无需人工干预
- **上下文管理**: 个性化Agent上下文和全局对话历史的智能管理
- **消息传递控制**: 完全自主的Agent间消息传递和协作控制
- **对话总结分析**: LLM驱动的对话历史总结和分析

#### �️ **简化工具集成架构**
- **AutoGen原生集成**: 基于AutoGen的原生工具调用机制，无需复杂的工具管理器
- **Python函数工具**: 直接使用Python异步函数作为工具，简化开发和维护
- **类型安全**: 使用类型注解提供更好的IDE支持和错误检查
- **统一工具接口**: 所有Agent共享相同的工具集，简化权限管理

#### 🎯 **分析流程控制器**
- **6步流程控制**: 严格按照README定义的6步分析流程执行
- **会话状态管理**: 完整的分析会话生命周期管理
- **错误处理和恢复**: 健壮的错误处理机制，支持分析过程的恢复
- **进度跟踪**: 实时的分析进度跟踪和状态报告

#### 🖥️ **用户交互系统**
- **命令行界面**: 当前主要的用户交互方式，支持交互式和批处理模式
- **统一UI抽象**: 为未来扩展Web界面或GUI预留的统一接口
- **用户代理**: 处理用户确认、信息补充等交互需求

## 🔄 6步分析流程

系统严格按照以下6步流程执行安全分析任务：

### 1️⃣ **用户输入阶段**
- 用户通过命令行界面输入分析任务或安全事件描述
- 系统创建新的分析会话，初始化主任务管理器
- 记录用户请求和相关元数据

### 2️⃣ **记忆检索阶段**
- 记忆管理系统自动搜索相关的历史案例、分析模板和解决方案
- 通过Mem0向量检索找到语义相关的知识
- 为PlanningAgent提供参考信息和最佳实践

### 3️⃣ **计划制定阶段**
- **PlanningAgent**基于用户请求和历史参考制定详细分析计划
- 将复杂任务分解为**最小粒度**的可执行子任务清单
- 创建结构化的分析策略和执行路径

### 4️⃣ **主任务执行循环**
循环执行直到所有任务完成：

#### 4-1. **任务确定**
- **ReflectionAgent**根据任务清单确定当前需要执行的子任务
- 创建子任务管理器，初始化子任务执行环境

#### 4-2. **子任务执行循环**
通过**EnhancedGroupChatManager**智能协调Agent协作：
- **4-2-1**: **ThreatAnalysisAgent**分析子任务，制定技术分析方案
- **4-2-2**: **ToolExecutionAgent**执行具体安全工具，收集数据
- **4-2-3**: **ThreatAnalysisAgent**汇总工具结果，生成分析结论
- **4-2-4**: **ReflectionAgent**验证结果质量，决定下一步行动

#### 4-3. **任务状态更新**
- 记忆管理系统更新Agent上下文和任务状态
- 根据ReflectionAgent评估决定继续执行、任务完成或重新规划

### 5️⃣ **报告生成阶段**
- **ReportAgent**汇总所有分析结果和执行过程
- 生成专业的安全分析报告，包含影响评估和响应建议
- 格式化输出，支持多种报告格式

### 6️⃣ **知识存储阶段**
- 将分析报告和关键知识存储到长期记忆
- 通过Mem0建立知识关联，为后续分析提供参考
- 更新分析案例库和最佳实践模板

## 🚀 快速开始

### 📋 环境要求
- Python 3.10+
- 8GB+ 内存推荐
- 支持的操作系统：Windows、Linux、macOS

### 🔧 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd traffic_analysis_agent
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置模型接口**
编辑 `src/config.py` 文件，配置您的LLM接口：
```python
LITELLM = {
    "base_url": "your-llm-endpoint",
    "api_key": "your-api-key",
    # ... 其他配置
}
```

4. **启动系统**
```bash
# 交互式模式
python main_enhanced.py

# 批处理模式
python main_enhanced.py "分析可疑网络流量"
```

### 💡 使用示例

#### 交互式分析
```bash
$ python main_enhanced.py
🚀 正在初始化增强版安全分析系统...
✅ 增强版安全分析系统初始化完成

请输入您的安全分析需求: 分析最近的异常登录事件
```

#### 批处理分析
```bash
$ python main_enhanced.py "检查防火墙日志中的异常流量模式"
```

## 📁 项目结构

```
traffic_analysis_agent/
├── main_enhanced.py              # 主应用入口
├── src/                         # 核心源码
│   ├── config.py               # 系统配置
│   ├── core/                   # 核心组件
│   │   ├── analysis_flow_controller.py    # 分析流程控制器
│   │   ├── enhanced_groupchat_manager.py  # 增强版GroupChat管理器
│   │   ├── task_manager.py               # 任务管理系统
│   │   └── user_proxies.py              # 用户交互代理
│   ├── agent_settings/         # Agent配置文件
│   ├── tools/                  # 工具函数集合
│   └── extensions/             # 扩展模块
│       └── memory/            # 记忆管理系统
├── design_docs/                # 设计文档
├── memory_storage/             # 记忆数据存储
└── requirements.txt           # 依赖列表
```

## 🔧 配置说明

### 模型配置
系统支持多种LLM模型，通过LiteLLM代理统一接入：
- **Chat模型**: deepseek-chat, claude-sonnet-4, gemini-2.5-flash等
- **Reasoning模型**: deepseek-reasoner, gemini-2.5-flash-thinking等

### 记忆配置
混合记忆架构配置：
- **本地存储**: `./memory_storage/sessions/` (JSON格式)
- **向量存储**: `./memory_storage/mem0_data/` (ChromaDB)

---

## 🙏 致谢

本项目灵感来源于多个优秀开源项目：
- [AutoGen](https://github.com/microsoft/autogen) - Multi-Agent协作框架
- [Mem0](https://github.com/mem0ai/mem0) - AI记忆管理系统
- [LiteLLM](https://github.com/BerriAI/litellm) - 统一LLM接口代理

---

**让AI为网络安全赋能，构建更安全的数字世界** 🛡️✨
