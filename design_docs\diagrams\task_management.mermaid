graph TB
    subgraph "双层任务管理架构 (Dual-Layer Task Management)"
        subgraph "主任务管理器 (MainTaskManager)"
            MAIN_MGR[主任务管理器<br/>MainTaskManager<br/>📊 分析会话管理]
            SESSION_ID[会话ID<br/>session_id<br/>🆔 唯一标识]
            USER_REQ[用户请求<br/>user_request<br/>📝 原始需求]
            ANALYSIS_PLAN[分析计划<br/>analysis_plan<br/>📋 Markdown格式]
            SUB_MANAGERS[子任务管理器集合<br/>sub_task_managers<br/>🗂️ Dict[str, SubTaskManager]]
            EXEC_RESULTS[执行结果列表<br/>execution_results<br/>📊 List[Dict]]
            FINAL_REPORT[最终报告<br/>final_report<br/>📄 ReportAgent输出]
        end

        subgraph "子任务管理器 (SubTaskManager)"
            SUB_MGR[子任务管理器<br/>SubTaskManager<br/>🔧 具体任务执行]
            TASK_TITLE[任务标题<br/>task_title<br/>📝 ReflectionAgent确定]
            TOOL_EXECUTIONS[工具执行记录<br/>tool_executions<br/>🛠️ ToolExecutionAgent]
            DATA_COLLECTIONS[数据收集记录<br/>data_collections<br/>📊 数据汇总]
            ANALYSIS_CONCLUSIONS[分析结论记录<br/>analysis_conclusions<br/>🧠 ThreatAnalysisAgent]
        end
    end

    subgraph "任务状态管理 (Task Status Management)"
        PENDING[⏳ 待处理<br/>PENDING<br/>任务已创建未开始]
        PROGRESS[🔄 处理中<br/>IN_PROGRESS<br/>任务正在执行]
        COMPLETED[✅ 已完成<br/>COMPLETED<br/>任务成功完成]
        FAILED[❌ 失败<br/>FAILED<br/>任务执行失败]
        CANCELLED[🚫 已取消<br/>CANCELLED<br/>任务被取消]
        BLOCKED[⚠️ 阻塞<br/>BLOCKED<br/>任务被阻塞]
    end

    subgraph "任务创建和管理 (Task Creation & Management)"
        FLOW_CTRL[分析流程控制器<br/>AnalysisFlowController<br/>🎯 任务管理器创建]
        DIRECT_CREATE[直接创建模式<br/>Direct Creation<br/>🔧 无需工厂类]
        PERSISTENCE[持久化管理<br/>TaskManagerPersistence<br/>💾 JSON文件存储]
        RECOVERY[恢复机制<br/>Session Recovery<br/>🔄 状态恢复]
    end

    subgraph "实际执行流程 (Actual Execution Flow)"
        PLAN_CREATE[计划制定阶段<br/>PlanningAgent<br/>📋 创建主任务管理器]
        TASK_DETERMINE[任务确定阶段<br/>ReflectionAgent<br/>🔍 创建子任务管理器]
        COLLAB_EXECUTE[协作执行阶段<br/>EnhancedGroupChatManager<br/>🤖 记录执行过程]
        RESULT_EVALUATE[结果评估阶段<br/>ReflectionAgent<br/>✅ 更新任务状态]
        REPORT_GENERATE[报告生成阶段<br/>ReportAgent<br/>📄 汇总最终结果]
    end

    subgraph "任务数据结构 (Task Data Structure)"
        BASE_TASK[BaseTaskManager<br/>基础任务管理器<br/>🏗️ 抽象基类]
        TASK_ITEM[TaskItem<br/>任务项<br/>📝 具体任务数据]
        TASK_STATUS[TaskStatus枚举<br/>任务状态<br/>🔄 状态管理]
        TASK_PRIORITY[TaskPriority枚举<br/>任务优先级<br/>⭐ 优先级管理]
        EXPORT_DATA[数据导出<br/>export_full_data()<br/>💾 完整数据序列化]
    end

    %% 双层管理器关系
    MAIN_MGR --> SUB_MGR
    SESSION_ID --> MAIN_MGR
    USER_REQ --> MAIN_MGR
    ANALYSIS_PLAN --> MAIN_MGR
    SUB_MANAGERS --> SUB_MGR
    EXEC_RESULTS --> MAIN_MGR
    FINAL_REPORT --> MAIN_MGR

    %% 子任务管理器内部关系
    TASK_TITLE --> SUB_MGR
    TOOL_EXECUTIONS --> SUB_MGR
    DATA_COLLECTIONS --> SUB_MGR
    ANALYSIS_CONCLUSIONS --> SUB_MGR

    %% 状态转换流程
    PENDING --> PROGRESS
    PROGRESS --> COMPLETED
    PROGRESS --> FAILED
    PROGRESS --> BLOCKED
    BLOCKED --> PROGRESS
    PENDING --> CANCELLED
    PROGRESS --> CANCELLED

    %% 任务创建流程
    FLOW_CTRL --> DIRECT_CREATE
    DIRECT_CREATE --> MAIN_MGR
    DIRECT_CREATE --> SUB_MGR
    PERSISTENCE --> RECOVERY

    %% 实际执行流程
    PLAN_CREATE --> MAIN_MGR
    TASK_DETERMINE --> SUB_MGR
    COLLAB_EXECUTE --> TOOL_EXECUTIONS
    COLLAB_EXECUTE --> DATA_COLLECTIONS
    COLLAB_EXECUTE --> ANALYSIS_CONCLUSIONS
    RESULT_EVALUATE --> TASK_STATUS
    REPORT_GENERATE --> FINAL_REPORT

    %% 数据结构继承关系
    BASE_TASK --> MAIN_MGR
    BASE_TASK --> SUB_MGR
    TASK_ITEM --> BASE_TASK
    TASK_STATUS --> TASK_ITEM
    TASK_PRIORITY --> TASK_ITEM

    %% 持久化关系
    MAIN_MGR --> EXPORT_DATA
    SUB_MGR --> EXPORT_DATA
    EXPORT_DATA --> PERSISTENCE

    %% 结果汇总流程
    TOOL_EXECUTIONS --> EXEC_RESULTS
    DATA_COLLECTIONS --> EXEC_RESULTS
    ANALYSIS_CONCLUSIONS --> EXEC_RESULTS
    EXEC_RESULTS --> FINAL_REPORT

    %% 样式定义
    classDef mainManager fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000000
    classDef subManager fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000000
    classDef statusPending fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000000
    classDef statusActive fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000000
    classDef statusComplete fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000000
    classDef statusError fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000000
    classDef management fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000000
    classDef execution fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000000
    classDef structure fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000000

    %% 应用样式
    class MAIN_MGR,SESSION_ID,USER_REQ,ANALYSIS_PLAN,SUB_MANAGERS,EXEC_RESULTS,FINAL_REPORT mainManager
    class SUB_MGR,TASK_TITLE,TOOL_EXECUTIONS,DATA_COLLECTIONS,ANALYSIS_CONCLUSIONS subManager
    class PENDING statusPending
    class PROGRESS,BLOCKED statusActive
    class COMPLETED statusComplete
    class FAILED,CANCELLED statusError
    class FLOW_CTRL,DIRECT_CREATE,PERSISTENCE,RECOVERY management
    class PLAN_CREATE,TASK_DETERMINE,COLLAB_EXECUTE,RESULT_EVALUATE,REPORT_GENERATE execution
    class BASE_TASK,TASK_ITEM,TASK_STATUS,TASK_PRIORITY,EXPORT_DATA structure
