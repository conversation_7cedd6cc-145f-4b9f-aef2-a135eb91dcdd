sequenceDiagram
    participant User as 👤 用户
    participant Flow as 🎯 分析流程控制器
    participant MemTools as 🧠 记忆管理系统
    participant Planning as 📋 PlanningAgent
    participant Reflection as 🔍 ReflectionAgent
    participant Threat as 🛡️ ThreatAnalysisAgent
    participant Traffic as 🌐 TrafficLogAgent
    participant Code as 💻 CodeExecutionAgent
    participant Report as 📊 ReportAgent
    participant GroupChat as 🤖 EnhancedGroupChat

    Note over User,Report: 🚀 6步分析流程开始

    %% 步骤1: 用户输入
    User->>Flow: 1️⃣ 输入任务/指令
    Flow->>Flow: 创建分析会话

    %% 步骤2: 记忆检索 (工具化)
    Note over Flow,MemTools: 📚 步骤2: 记忆检索 (工具化实现)
    Flow->>MemTools: 2️⃣ 搜索相关案例、模板、方案
    MemTools->>MemTools: 本地JSON + Mem0向量检索
    MemTools-->>Flow: 返回记忆上下文

    %% 步骤3: 计划制定
    Note over Flow,Planning: 🎯 步骤3: 计划制定 (固定调用)
    Flow->>Planning: 3️⃣ 制定分析计划和任务清单
    Note right of Planning: 输入: 用户请求 + 记忆上下文<br/>工具: 记忆管理工具集
    Planning->>MemTools: 使用记忆工具获取上下文
    MemTools-->>Planning: 返回Agent专用上下文
    Planning->>Planning: 制定详细分析计划
    Planning-->>Flow: 返回Markdown格式分析计划
    Flow->>Flow: 创建主任务管理器
    
    %% 步骤4: 主任务执行循环
    Note over Flow,Tool: 🔄 步骤4: 主任务执行循环
    
    loop 主任务循环 (直到所有任务完成)
        %% 4-1: 任务确定
        Note over Flow,Reflection: 🔍 4-1: 任务确定 (固定调用)
        Flow->>Reflection: 4️⃣-1️⃣ 确定当前需要执行的任务
        Note right of Reflection: 输入: 分析计划 + 执行历史<br/>工具: 记忆管理工具集
        Reflection->>MemTools: 使用记忆工具获取上下文
        MemTools-->>Reflection: 返回Agent专用上下文
        Reflection->>Reflection: 分析任务清单和执行状态
        Reflection-->>Flow: 返回当前任务 或 "ALL_TASKS_COMPLETED"
        
        alt 所有任务已完成
            Note over Flow: ✅ 退出主任务循环
        else 有任务需要执行
            Flow->>Flow: 创建子任务管理器
            
            %% 4-2: 子任务执行循环
            Note over Flow,Tool: 🔧 4-2: 子任务执行循环
            
            loop 子任务循环 (直到任务完成)
                %% 4-2-1到4-2-4: 智能Agent协作 (唯一智能选择阶段)
                Note over GroupChat,Tool: 🤖 4-2-1到4-2-4: 智能Agent协作 (唯一智能选择阶段)
                Flow->>GroupChat: 启动子任务协作
                Note right of GroupChat: 完全自主的Agent选择和协作

                loop Agent智能协作
                    GroupChat->>GroupChat: LLM智能选择下一个Agent
                    alt 选择ThreatAnalysisAgent
                        GroupChat->>Threat: 威胁分析和策略制定
                        Threat->>MemTools: 使用记忆工具
                        MemTools-->>Threat: 返回专用上下文
                        Threat->>Threat: 制定分析方案
                        Threat-->>GroupChat: 分析结果和工具指导
                    else 选择ToolExecutionAgent
                        GroupChat->>Tool: 执行具体安全工具
                        Tool->>MemTools: 使用记忆工具
                        MemTools-->>Tool: 返回专用上下文
                        Tool->>Tool: 调用OpenSearch等工具
                        Tool-->>GroupChat: 工具执行结果
                    end
                    GroupChat->>GroupChat: 判断协作是否完成
                end

                GroupChat-->>Flow: 完整的协作结果和分析结论

                %% 4-2-5: 质量评估
                Note over Flow,Reflection: ✅ 4-2-5: 质量评估 (固定调用)
                Flow->>Reflection: 4️⃣-2️⃣-5️⃣ 质量评估和结果验证
                Note right of Reflection: 输入: 当前任务 + 协作结果<br/>工具: 记忆管理工具集
                Reflection->>MemTools: 使用记忆工具保存交互
                MemTools-->>Reflection: 记忆保存确认
                Reflection->>Reflection: 评估完成度和质量
                Reflection-->>Flow: TaskAction决策

                %% 记忆自动更新 (工具化)
                Note over Flow,MemTools: 💾 记忆自动更新 (工具化实现)
                Flow->>MemTools: 自动保存Agent交互记录
                MemTools->>MemTools: 更新Agent专用会话
                MemTools-->>Flow: 记忆更新确认
                
                %% 判断下一步行动
                alt 任务完成
                    Note over Flow: ✅ 退出子任务循环
                else 继续执行
                    Note over Flow: 🔄 继续子任务循环
                else 需要重新规划
                    Note over Flow: 📋 需要重新规划
                    Flow->>Planning: 更新分析计划
                    Planning-->>Flow: 更新后的计划
                end
            end
        end
    end
    
    %% 步骤5: 报告生成
    Note over Flow,Report: 📄 步骤5: 报告生成 (固定调用)
    Flow->>Report: 5️⃣ 生成分析报告
    Note right of Report: 输入: 完整分析过程和结果<br/>工具: 记忆管理工具集
    Report->>MemTools: 使用记忆工具获取上下文
    MemTools-->>Report: 返回专用上下文和案例
    Report->>Report: 撰写专业安全分析报告
    Report-->>Flow: 最终分析报告

    %% 步骤6: 知识存储 (工具化)
    Note over Flow,MemTools: 🗄️ 步骤6: 知识存储 (工具化实现)
    Flow->>MemTools: 6️⃣ 存储分析报告和知识
    MemTools->>MemTools: 存储到Mem0长期记忆
    MemTools->>MemTools: 建立知识关联和索引
    MemTools-->>Flow: 知识存储确认
    
    %% 返回结果
    Flow-->>User: 🎉 返回分析结果
    
    Note over User,Report: ✨ 6步分析流程完成
