graph TB
    subgraph "用户交互层 (User Interface Layer)"
        UI[用户界面管理器<br/>UIManager]
        CLI[命令行界面<br/>CLIUserInterface]
        USER_PROXY[用户代理<br/>CommandLineUserProxy]
        WEB[Web界面预留<br/>WebUserInterface]
    end

    subgraph "应用控制层 (Application Control Layer)"
        APP[主应用入口<br/>EnhancedSecurityAnalysisApp]
        FLOW[分析流程控制器<br/>AnalysisFlowController<br/>🔄 6步流程控制]
    end

    subgraph "Agent协作层 (Agent Collaboration Layer)"
        GROUPCHAT[增强GroupChat管理器<br/>EnhancedGroupChatManager<br/>🤖 智能Agent选择]

        subgraph "5个专业化Agent"
            PLANNING[计划制定Agent<br/>PlanningAgent<br/>🧠 deepseek-reasoner<br/>📝 记忆工具集成]
            REFLECTION[反思验证Agent<br/>ReflectionAgent<br/>🧠 deepseek-reasoner<br/>📝 记忆工具集成]
            THREAT[威胁分析Agent<br/>ThreatAnalysisAgent<br/>🧠 deepseek-reasoner<br/>📝 记忆工具集成]
            TOOL[工具执行Agent<br/>ToolExecutionAgent<br/>💬 deepseek-chat<br/>🛠️ OpenSearch工具<br/>📝 记忆工具集成]
            REPORT[报告生成Agent<br/>ReportAgent<br/>💬 deepseek-chat<br/>📝 记忆工具集成]
        end
    end

    subgraph "核心服务层 (Core Service Layer)"
        MAIN_TASK[主任务管理器<br/>MainTaskManager<br/>📊 分析计划管理]
        SUB_TASK[子任务管理器<br/>SubTaskManager<br/>🔧 工具执行记录]

        subgraph "混合记忆管理系统"
            MEM_TOOLS[记忆工具集<br/>MemoryTools<br/>🔧 工具化实现]
            SESSION_MGR[会话管理器<br/>SessionManager<br/>📁 本地JSON存储]
            MEM_PROC[记忆处理器<br/>MemoryProcessor<br/>🧠 Mem0集成]
            MEM_ADAPTER[记忆适配器<br/>MemoryIntegrationAdapter<br/>🔗 统一接口]
        end
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        CONFIG[配置管理<br/>Config<br/>🔧 集中化配置]
        MODEL[模型客户端<br/>OpenAIChatCompletionClient<br/>🤖 AutoGen集成]
        LITELLM[LiteLLM代理<br/>统一模型接口<br/>🌐 多模型支持]

        subgraph "存储系统"
            JSON_STORE[本地JSON存储<br/>./memory_storage/sessions/<br/>📁 对话记录]
            CHROMA_DB[ChromaDB向量存储<br/>./memory_storage/mem0_data/<br/>🧠 Mem0向量记忆]
        end
    end

    %% 用户交互层连接
    UI --> CLI
    UI --> USER_PROXY
    UI --> WEB

    %% 应用控制层连接
    CLI --> APP
    USER_PROXY --> APP
    APP --> FLOW

    %% 6步流程控制连接
    FLOW -.->|步骤1-2| MEM_TOOLS
    FLOW -.->|步骤3| PLANNING
    FLOW -.->|步骤4-1,4-2-5| REFLECTION
    FLOW -.->|步骤4-2| GROUPCHAT
    FLOW -.->|步骤5| REPORT
    FLOW -.->|步骤6| MEM_TOOLS

    %% 智能Agent选择（仅子任务阶段）
    GROUPCHAT -.->|智能选择| THREAT
    GROUPCHAT -.->|智能选择| TOOL

    %% 记忆工具集成
    PLANNING --> MEM_TOOLS
    REFLECTION --> MEM_TOOLS
    THREAT --> MEM_TOOLS
    TOOL --> MEM_TOOLS
    REPORT --> MEM_TOOLS

    %% 任务管理连接
    FLOW --> MAIN_TASK
    FLOW --> SUB_TASK
    MAIN_TASK --> SUB_TASK

    %% 记忆系统内部连接
    MEM_TOOLS --> MEM_ADAPTER
    MEM_ADAPTER --> SESSION_MGR
    MEM_ADAPTER --> MEM_PROC
    SESSION_MGR --> JSON_STORE
    MEM_PROC --> CHROMA_DB

    %% 基础设施连接
    FLOW --> MODEL
    MODEL --> LITELLM
    MAIN_TASK --> JSON_STORE
    SUB_TASK --> JSON_STORE

    %% 样式定义
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000
    classDef appLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef agentLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef serviceLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef infraLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    classDef memoryLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px,color:#000000
    classDef reasoningAgent fill:#ffecb3,stroke:#f57f17,stroke-width:3px,color:#000000
    classDef chatAgent fill:#c8e6c9,stroke:#388e3c,stroke-width:3px,color:#000000
    classDef flowControl fill:#e8eaf6,stroke:#3f51b5,stroke-width:3px,color:#000000

    %% 应用样式
    class UI,CLI,USER_PROXY,WEB userLayer
    class APP appLayer
    class FLOW flowControl
    class GROUPCHAT agentLayer
    class PLANNING,REFLECTION,THREAT reasoningAgent
    class TOOL,REPORT chatAgent
    class MAIN_TASK,SUB_TASK serviceLayer
    class MEM_TOOLS,SESSION_MGR,MEM_PROC,MEM_ADAPTER memoryLayer
    class CONFIG,MODEL,LITELLM,JSON_STORE,CHROMA_DB infraLayer
