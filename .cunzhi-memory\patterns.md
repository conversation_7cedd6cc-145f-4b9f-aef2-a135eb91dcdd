# 常用模式和最佳实践

- 新增OpenSearch查询构建辅助工具集，包含13个查询构建函数：基础条件构建工具(build_match_condition, build_term_condition, build_terms_condition, build_range_condition, build_wildcard_condition, build_exists_condition, build_time_range_condition)、复合查询构建工具(build_opensearch_query, build_complex_query)、专用查询构建工具(build_traffic_analysis_query, build_security_event_query)、查询验证工具(validate_query_syntax)。这些工具可以避免LLM直接生成复杂查询语句导致的语法错误，提高查询成功率。已更新TrafficLogAgent配置集成所有新工具，并创建了详细的使用指南文档。
- 单轮对话上下文重组系统：所有Agent通过BaseManagedAgent统一处理上下文，根据传入的MainTaskManager/SubTaskManager自动判断主流程/子流程上下文。主流程包含任务清单+历史子任务结果，子流程包含任务清单+当前任务+对话历史+摘要。所有对话历史自动存储到SubTaskManager，记忆自动注入，实现真正的单轮对话（system message + user message）格式。
- 增强记忆结构：在Session和ConversationMessage中新增Agent名称、任务类型(main_task/sub_task)、任务清单、执行的子任务等字段。Session级别包含primary_agent、task_type、task_list、executed_subtasks、analysis_plan、user_request。Message级别包含agent_name、agent_role、task_type、task_id、task_title。SubTaskManager的add_agent_conversation自动添加任务信息，store_analysis_case存储完整的分析案例包含所有Agent和任务元数据。
- 记忆和上下文系统优化：实现自动消息摘要生成(不超过100字)、上下文长度控制和分层摘要(阈值5000字符)、分析次数限制(主流程10次/子流程20次)和进度提示、基于摘要而非原始消息组织上下文。SubTaskManager自动生成message_summary和analysis_sequence，_get_optimized_conversation_history支持摘要压缩，Agent上下文包含分析进度控制信息，提升系统效率和用户体验。
- 记忆和上下文系统修正：1.摘要生成改为使用LLM(MemoryProcessor.generate_summary)而非简单文本处理，提供高质量智能摘要；2.统一循环控制机制，主流程(max_main_iterations)、子流程(max_analysis_count)、GroupChat(max_rounds)均设置为10次，确保一致性；3.进度跟踪消息格式统一，包含当前次数和最大次数信息；4.SubTaskManager集成MemoryProcessor实现LLM摘要生成，fallback机制保证稳定性。
- 记忆和上下文系统优化完成：1.统一循环控制配置-在src/config.py中新增LOOP_CONTROL配置节，包含max_main_iterations、max_sub_iterations、context_compression_threshold、progress_message_template等设置，所有任务管理器和GroupChat都从配置读取，确保一致性；2.LLM摘要生成机制完善-SubTaskManager._generate_message_summary()和EnhancedGroupChatManager._summarize_conversation()都使用LLM生成高质量摘要，MemoryProcessor提供统一接口；3.智能上下文组织-_get_optimized_conversation_history()根据5000字符阈值智能选择完整对话vs摘要版本，保留最近10条完整信息；4.进度提示统一-所有模块使用配置中的progress_message_template格式化进度消息，用户体验一致；5.系统集成测试6/6全部通过，验证了配置加载、任务管理器、记忆处理器、上下文构建、摘要生成、循环控制一致性等功能正常工作
- 实现了详细的LLM交互日志系统，每个Agent的LLM输出都会打印到控制台，包括Agent名称、模型名称、交互类型（REQUEST/RESPONSE/REASONING/TOOL_CALL等）和完整内容，支持自动截断长内容，便于分析调试Agent执行过程
