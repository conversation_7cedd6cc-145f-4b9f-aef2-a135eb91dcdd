"""
记忆管理系统集成适配器

用于将记忆管理系统集成到现有的Agent系统中
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json

from src.extensions.memory.memory_tools import MemoryTools
from src.utils.logger import get_logger

class MemoryIntegrationAdapter:
    """记忆管理系统集成适配器"""
    
    def __init__(self):
        self.memory_tools = MemoryTools()
        self._active_sessions: Dict[str, str] = {}  # agent_id -> session_id 映射
        self.logger = get_logger("memory_integration_adapter")
    
    def create_agent_session(self, agent_id: str, description: str, 
                           metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        为Agent创建专用会话
        
        Args:
            agent_id: Agent ID
            description: 会话描述
            metadata: 会话元数据
            
        Returns:
            str: 会话ID
        """
        # 添加agent信息到元数据
        session_metadata = metadata or {}
        session_metadata.update({
            "agent_id": agent_id,
            "created_by": "integration_adapter",
            "session_type": "agent_session"
        })
        
        result = self.memory_tools.create_session(description, session_metadata)
        
        if result['success']:
            session_id = result['session_id']
            self._active_sessions[agent_id] = session_id
            self.logger.info(f"🤖 为Agent {agent_id} 创建会话: {session_id}")
            # print(f"🤖 为Agent {agent_id} 创建会话: {session_id}")
            return session_id
        else:
            raise Exception(f"创建Agent会话失败: {result.get('error', 'Unknown error')}")
    
    def get_agent_session(self, agent_id: str) -> Optional[str]:
        """
        获取Agent的活跃会话ID
        
        Args:
            agent_id: Agent ID
            
        Returns:
            Optional[str]: 会话ID，如果不存在返回None
        """
        return self._active_sessions.get(agent_id)
    
    def save_agent_conversation(self, agent_id: str, messages: List[Dict[str, Any]], 
                              auto_create_session: bool = True) -> bool:
        """
        保存Agent对话
        
        Args:
            agent_id: Agent ID
            messages: 对话消息
            auto_create_session: 如果会话不存在是否自动创建
            
        Returns:
            bool: 是否保存成功
        """
        session_id = self.get_agent_session(agent_id)
        
        # 如果会话不存在且允许自动创建
        if not session_id and auto_create_session:
            session_id = self.create_agent_session(
                agent_id, 
                f"Agent {agent_id} 自动创建的会话",
                {"auto_created": True}
            )
        
        if not session_id:
            self.logger.error(f"❌ Agent {agent_id} 没有活跃会话")
            # print(f"❌ Agent {agent_id} 没有活跃会话")
            return False
        
        # 为消息添加agent信息
        enhanced_messages = []
        for msg in messages:
            enhanced_msg = msg.copy()
            enhanced_msg.setdefault('metadata', {})
            enhanced_msg['metadata']['agent_id'] = agent_id
            enhanced_msg.setdefault('timestamp', datetime.now().isoformat())
            enhanced_messages.append(enhanced_msg)
        
        result = self.memory_tools.save_conversation(session_id, enhanced_messages)
        return result['success']
    
    def retrieve_agent_memories(self, agent_id: str, query: str, 
                              include_global: bool = False, limit: int = 10) -> List[Dict[str, Any]]:
        """
        检索Agent相关记忆
        
        Args:
            agent_id: Agent ID
            query: 查询内容
            include_global: 是否包含全局记忆
            limit: 结果限制
            
        Returns:
            List[Dict]: 记忆列表
        """
        session_id = self.get_agent_session(agent_id)
        
        result = self.memory_tools.retrieve_memories(
            query=query,
            session_id=session_id,
            global_search=include_global,
            limit=limit
        )
        
        if result['success']:
            return result['memories']
        else:
            self.logger.error(f"❌ Agent {agent_id} 记忆检索失败: {result.get('error')}")
            # print(f"❌ Agent {agent_id} 记忆检索失败: {result.get('error')}")
            return []
    
    def format_context_for_agent(self, agent_id: str, query: str, 
                                max_context_length: int = 2000) -> str:
        """
        为Agent格式化上下文信息
        
        Args:
            agent_id: Agent ID
            query: 当前查询
            max_context_length: 最大上下文长度
            
        Returns:
            str: 格式化的上下文
        """
        memories = self.retrieve_agent_memories(agent_id, query, include_global=True)
        
        if not memories:
            return "暂无相关历史记忆。"
        
        context_parts = []
        current_length = 0
        
        for memory in memories:
            content = memory.get('content', '')
            session_id = memory.get('session_id', '')
            relevance = memory.get('relevance_score', 0.0)
            
            # 格式化记忆条目
            if session_id == self.get_agent_session(agent_id):
                memory_type = "本会话记忆"
            else:
                memory_type = "历史记忆"
            
            formatted_memory = f"[{memory_type}] {content} (相关度: {relevance:.2f})"
            
            if current_length + len(formatted_memory) > max_context_length:
                break
            
            context_parts.append(formatted_memory)
            current_length += len(formatted_memory)
        
        context = "相关历史记忆:\n" + "\n".join(context_parts)
        
        if current_length >= max_context_length:
            context += f"\n\n[上下文已截断，原始记忆数: {len(memories)}]"
        
        return context
    
    def save_agent_interaction(self, agent_id: str, user_input: str, agent_response: str,
                             reasoning: Optional[str] = None, tool_calls: Optional[List[Dict]] = None,
                             tool_responses: Optional[List[Dict]] = None) -> bool:
        """
        保存Agent交互记录
        
        Args:
            agent_id: Agent ID
            user_input: 用户输入
            agent_response: Agent响应
            reasoning: 推理过程
            tool_calls: 工具调用列表
            tool_responses: 工具响应列表
            
        Returns:
            bool: 是否保存成功
        """
        messages = []
        
        # 用户消息
        user_msg = {
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now().isoformat()
        }
        messages.append(user_msg)
        
        # Agent响应消息
        agent_msg = {
            "role": "assistant",
            "content": agent_response,
            "timestamp": datetime.now().isoformat(),
            "reasoning": reasoning
        }
        
        # 添加工具调用信息
        if tool_calls:
            agent_msg["tool_call"] = {
                "calls": tool_calls,
                "count": len(tool_calls)
            }
        
        if tool_responses:
            agent_msg["tool_response"] = {
                "responses": tool_responses,
                "count": len(tool_responses)
            }
        
        messages.append(agent_msg)
        
        return self.save_agent_conversation(agent_id, messages)
    
    def get_agent_session_summary(self, agent_id: str) -> Optional[str]:
        """
        获取Agent会话总结
        
        Args:
            agent_id: Agent ID
            
        Returns:
            Optional[str]: 会话总结
        """
        session_id = self.get_agent_session(agent_id)
        if not session_id:
            return None
        
        result = self.memory_tools.get_session_info(session_id)
        if result['success']:
            return result['session_info'].get('overall_summary')
        
        return None
    
    def list_agent_messages(self, agent_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        列出Agent的消息记录

        Args:
            agent_id: Agent ID
            limit: 返回数量限制

        Returns:
            List[Dict]: 消息记录列表
        """
        session_id = self.get_agent_session(agent_id)
        if not session_id:
            return []

        # 获取指定数量的最新消息
        result = self.memory_tools.query_messages(session_id, 0, limit)
        if result['success']:
            return result.get('messages', [])

        return []
    
    def cleanup_agent_session(self, agent_id: str) -> bool:
        """
        清理Agent会话（从活跃会话中移除，但不删除数据）
        
        Args:
            agent_id: Agent ID
            
        Returns:
            bool: 是否清理成功
        """
        if agent_id in self._active_sessions:
            session_id = self._active_sessions[agent_id]
            del self._active_sessions[agent_id]
            self.logger.info(f"🧹 清理Agent {agent_id} 的活跃会话: {session_id}")
            # print(f"🧹 清理Agent {agent_id} 的活跃会话: {session_id}")
            return True
        
        return False
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """
        获取系统统计信息
        
        Returns:
            Dict: 统计信息
        """
        # 获取会话列表
        sessions_result = self.memory_tools.list_sessions(limit=1000)
        
        stats = {
            "active_agent_sessions": len(self._active_sessions),
            "total_sessions": 0,
            "agent_sessions": 0,
            "total_messages": 0,
            "active_agents": list(self._active_sessions.keys())
        }
        
        if sessions_result['success']:
            sessions = sessions_result['sessions']
            stats["total_sessions"] = len(sessions)
            
            # 统计Agent会话和消息数
            for session in sessions:
                metadata = session.get('metadata', {})
                if metadata.get('session_type') == 'agent_session':
                    stats["agent_sessions"] += 1

                stats["total_messages"] += session.get('message_count', 0)
        
        return stats


# 全局适配器实例
memory_adapter = MemoryIntegrationAdapter()
