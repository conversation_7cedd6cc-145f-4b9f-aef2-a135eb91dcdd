# 📊 架构图表说明

## 概述

本目录包含网络安全Multi-Agent智能分析平台的所有架构图表，使用Mermaid格式绘制。

## 📁 图表文件列表

### 设计文档结构图
- **文件**: `design_docs_structure.mermaid`
- **用途**: 展示整个设计文档体系的组织结构和关系
- **内容**: 文档分类、架构层次、核心流程、设计原则

### 系统架构图
- **详细版**: `system_architecture.mermaid`
- **简化版**: `system_architecture_simple.mermaid` ⭐ **推荐查看**
- **用途**: 展示系统的5层架构和组件关系
- **内容**: 用户交互层、应用控制层、Agent协作层、核心服务层、基础设施层

### Agent流转流程图
- **文件**: `agent_flow.mermaid`
- **用途**: 详细展示6步分析流程和Agent交互
- **内容**: 时序图展示完整的分析流程，包括固定调用和智能选择

### 上下文管理架构图
- **文件**: `context_management.mermaid`
- **用途**: 展示记忆管理和上下文传递机制
- **内容**: 长短时记忆、上下文处理、Agent专用上下文、质量控制

### 任务管理架构图
- **文件**: `task_management.mermaid`
- **用途**: 展示分层任务管理系统
- **内容**: 主任务管理器、子任务管理器、状态管理、工厂模式

### 上下文流转流程图
- **文件**: `context_flow.mermaid`
- **用途**: 展示上下文在系统中的流转路径
- **内容**: 上下文生成、处理、传递、更新的完整流程

## 🖥️ 查看方式

### 1. VSCode中查看 (推荐)
1. 安装Mermaid Preview扩展
2. 打开.mermaid文件
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入 "Mermaid: Preview" 并选择

### 2. 在线查看
1. 复制.mermaid文件内容
2. 访问 [Mermaid Live Editor](https://mermaid.live/)
3. 粘贴内容并查看

### 3. 导出为图片
1. 在Mermaid Live Editor中编辑
2. 点击 "Actions" → "Download SVG/PNG"
3. 保存为图片文件

## 🎨 图表样式说明

### 颜色编码
- **蓝色系**: 用户交互层组件
- **灰色系**: 应用控制层组件
- **绿色系**: Agent协作层组件
- **黄色系**: 核心服务层组件
- **粉色系**: 基础设施层组件

### 特殊标记
- **🧠**: Reasoning模型Agent (deepseek-reasoner)
- **💬**: Chat模型Agent (deepseek-chat)
- **⭐**: 推荐优先查看的文件

## 🔧 文字清晰度优化

所有图表都已优化文字显示：
- 添加了 `color:#000000` 确保文字为黑色
- 使用较浅的背景色提高对比度
- 简化版架构图减少了文字内容

### 如果文字仍然不清晰
1. **调整浏览器缩放**: 尝试放大到125%或150%
2. **使用简化版**: 查看 `system_architecture_simple.mermaid`
3. **导出为SVG**: 矢量格式在任何缩放下都清晰
4. **调整显示器设置**: 增加显示器的对比度

## 📝 图表维护

### 更新原则
1. **保持一致性**: 所有图表使用相同的样式和颜色编码
2. **及时更新**: 系统架构变更时同步更新图表
3. **版本控制**: 重大变更时保留旧版本作为参考

### 样式模板
```mermaid
%% 标准样式定义
classDef userLayer fill:#f0f8ff,stroke:#4682b4,stroke-width:2px,color:#000000
classDef appLayer fill:#f5f5f5,stroke:#696969,stroke-width:2px,color:#000000
classDef agentLayer fill:#f0fff0,stroke:#228b22,stroke-width:2px,color:#000000
classDef serviceLayer fill:#fff8dc,stroke:#daa520,stroke-width:2px,color:#000000
classDef infraLayer fill:#fff0f5,stroke:#c71585,stroke-width:2px,color:#000000
```

## 🚀 快速导航

### 新手入门
1. 先看 `design_docs_structure.mermaid` 了解整体结构
2. 再看 `system_architecture_simple.mermaid` 理解系统架构
3. 最后看 `agent_flow.mermaid` 理解工作流程

### 深入理解
1. `context_management.mermaid` - 理解记忆和上下文管理
2. `task_management.mermaid` - 理解任务管理机制
3. `context_flow.mermaid` - 理解数据流转过程

### 实现开发
1. 参考架构图理解组件关系
2. 查看流程图理解执行顺序
3. 结合设计文档进行具体实现

## 📞 技术支持

如果在查看图表时遇到问题：
1. 检查Mermaid语法是否正确
2. 确认查看工具支持Mermaid格式
3. 尝试不同的查看方式和工具
4. 参考本文档的查看指南

---

**最后更新**: 2024-12-19  
**维护者**: 系统架构团队
