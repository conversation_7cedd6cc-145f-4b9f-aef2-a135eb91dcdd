graph TB
    subgraph "用户交互层"
        UI[用户界面管理器]
        CLI[命令行界面]
        USER_PROXY[用户代理]
        WEB[Web界面预留]
    end

    subgraph "应用控制层"
        APP[主应用入口]
        FLOW[分析流程控制器<br/>6步流程控制]
    end

    subgraph "Agent协作层"
        GROUPCHAT[增强GroupChat管理器<br/>智能Agent选择]

        subgraph "5个专业化Agent"
            PLANNING[计划制定Agent<br/>deepseek-reasoner]
            REFLECTION[反思验证Agent<br/>deepseek-reasoner]
            THREAT[威胁分析Agent<br/>deepseek-reasoner]
            TOOL[工具执行Agent<br/>deepseek-chat]
            REPORT[报告生成Agent<br/>deepseek-chat]
        end
    end

    subgraph "核心服务层"
        MAIN_TASK[主任务管理器]
        SUB_TASK[子任务管理器]
        MEM_SYSTEM[混合记忆管理系统<br/>本地JSON + Mem0]
    end

    subgraph "基础设施层"
        CONFIG[配置管理]
        MODEL[模型客户端]
        LITELLM[LiteLLM代理]
        STORAGE[双层存储<br/>JSON + ChromaDB]
    end

    %% 连接关系
    UI --> CLI
    UI --> USER_PROXY
    UI --> WEB
    CLI --> APP
    USER_PROXY --> APP
    APP --> FLOW

    %% 6步流程控制
    FLOW -.->|步骤2,6| MEM_SYSTEM
    FLOW -.->|步骤3| PLANNING
    FLOW -.->|步骤4-1,4-2-5| REFLECTION
    FLOW -.->|步骤4-2| GROUPCHAT
    FLOW -.->|步骤5| REPORT

    %% 智能Agent选择（仅子任务阶段）
    GROUPCHAT -.->|智能选择| THREAT
    GROUPCHAT -.->|智能选择| TOOL

    %% 记忆工具集成
    PLANNING --> MEM_SYSTEM
    REFLECTION --> MEM_SYSTEM
    THREAT --> MEM_SYSTEM
    TOOL --> MEM_SYSTEM
    REPORT --> MEM_SYSTEM

    %% 任务管理
    FLOW --> MAIN_TASK
    FLOW --> SUB_TASK

    %% 基础设施
    FLOW --> MODEL
    MODEL --> LITELLM
    MAIN_TASK --> STORAGE
    SUB_TASK --> STORAGE
    MEM_SYSTEM --> STORAGE

    %% 样式定义
    classDef userLayer fill:#f0f8ff,stroke:#4682b4,stroke-width:2px,color:#000000
    classDef appLayer fill:#f5f5f5,stroke:#696969,stroke-width:2px,color:#000000
    classDef agentLayer fill:#f0fff0,stroke:#228b22,stroke-width:2px,color:#000000
    classDef serviceLayer fill:#fff8dc,stroke:#daa520,stroke-width:2px,color:#000000
    classDef infraLayer fill:#fff0f5,stroke:#c71585,stroke-width:2px,color:#000000
    classDef reasoningAgent fill:#fffacd,stroke:#ff8c00,stroke-width:2px,color:#000000
    classDef chatAgent fill:#f0fff0,stroke:#32cd32,stroke-width:2px,color:#000000

    %% 应用样式
    class UI,CLI,USER_PROXY,WEB userLayer
    class APP,FLOW appLayer
    class GROUPCHAT agentLayer
    class PLANNING,REFLECTION,THREAT reasoningAgent
    class TOOL,REPORT chatAgent
    class MAIN_TASK,SUB_TASK,MEM_SYSTEM serviceLayer
    class CONFIG,MODEL,LITELLM,STORAGE infraLayer
