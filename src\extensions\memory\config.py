"""
记忆管理系统配置
"""

import os
from typing import Dict, Any
from pathlib import Path


class MemoryConfig:
    """记忆管理系统配置类"""
    
    def __init__(self):
        # 存储路径配置
        self.storage_dir = Path("./memory_storage")
        self.sessions_dir = self.storage_dir / "sessions"
        self.mem0_storage_dir = self.storage_dir / "mem0_data"
        
        # 确保目录存在
        self.storage_dir.mkdir(exist_ok=True)
        self.sessions_dir.mkdir(exist_ok=True)
        self.mem0_storage_dir.mkdir(exist_ok=True)
        
        # LLM配置 - 用于生成摘要
        self.llm_config = {
            "provider": "custom",  # 使用自定义LLM接口
            "config": {
                "base_url": os.getenv("LLM_BASE_URL", "http://10.5.225.21:18089/v1"),
                "api_key": os.getenv("LLM_API_KEY", "sk-BP_3dbwmFuHI83DcJ-N5XA"),
                "model": os.getenv("LLM_MODEL", "secllm-v3"),
                "temperature": 0.1,
                "max_tokens": 500,
                "top_p": 0.1,
                "repetition_penalty": 1.05
            }
        }
        
        # Embedding配置
        self.embedding_config = {
            "model_name": os.getenv("EMBEDDING_MODEL", "bce-embedding-base-v1"),
            "base_url": os.getenv("EMBEDDING_BASE_URL", "http://10.24.107.199:5000/api/v1/ml/similarity"),
            "api_key": os.getenv("EMBEDDING_API_KEY", "key-placeholder")
        }
        
        # 向量数据库选择 - 支持多种向量数据库
        self.vector_store_type = os.getenv("VECTOR_STORE_TYPE", "qdrant")  # 默认使用qdrant

        # mem0配置 - 支持多种向量数据库
        self.mem0_config = {
            "vector_store": self._get_vector_store_config(),
            "llm": {
                "provider": "openai",
                "config": {
                    "model": self.llm_config["config"]["model"],
                    "openai_base_url": self.llm_config["config"]["base_url"],
                    "api_key": self.llm_config["config"]["api_key"],
                    "temperature": self.llm_config["config"]["temperature"],
                    "max_tokens": self.llm_config["config"]["max_tokens"],
                    "top_p": self.llm_config["config"]["top_p"],
                }
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "model": self.embedding_config["model_name"],
                    "openai_base_url": self.embedding_config["base_url"],
                    "api_key": self.embedding_config["api_key"]
                }
            }
            # 注意：根据mem0文档，telemetry应该通过环境变量控制，不在配置中设置
        }
        
        # 会话配置
        self.session_config = {
            "max_messages_per_session": 1000,  # 每个会话最大消息数
            "auto_summary_threshold": 10,  # 每10条消息自动生成摘要
            "max_summary_length": 200,  # 摘要最大长度
            "message_retention_days": 30  # 消息保留天数
        }
        
        # 记忆检索配置
        self.retrieval_config = {
            "max_results": 10,  # 最大检索结果数
            "similarity_threshold": 0.7,  # 相似度阈值
            "include_session_context": True,  # 是否包含会话上下文
            "global_search_enabled": True  # 是否启用全局搜索
        }
    
    def _get_vector_store_config(self) -> Dict[str, Any]:
        """根据环境变量选择向量数据库配置"""
        embedding_dims = int(os.getenv("EMBEDDING_DIMS", "768"))

        if self.vector_store_type.lower() == "qdrant":
            return {
                "provider": "qdrant",
                "config": {
                    "collection_name": "memory_collection",
                    "path": str(self.mem0_storage_dir / "qdrant_db"),
                    "embedding_model_dims": embedding_dims
                }
            }
        elif self.vector_store_type.lower() == "chroma":
            return {
                "provider": "chroma",
                "config": {
                    "collection_name": "memory_collection",
                    "path": str(self.mem0_storage_dir / "chroma_db")
                }
            }
        elif self.vector_store_type.lower() == "faiss":
            return {
                "provider": "faiss",
                "config": {
                    "collection_name": "memory_collection",
                    "path": str(self.mem0_storage_dir / "faiss_db"),
                    "embedding_model_dims": embedding_dims
                }
            }
        elif self.vector_store_type.lower() == "pinecone":
            return {
                "provider": "pinecone",
                "config": {
                    "index_name": os.getenv("PINECONE_INDEX_NAME", "memory-index"),
                    "api_key": os.getenv("PINECONE_API_KEY", ""),
                    "environment": os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp")
                }
            }
        elif self.vector_store_type.lower() == "weaviate":
            return {
                "provider": "weaviate",
                "config": {
                    "url": os.getenv("WEAVIATE_URL", "http://localhost:8080"),
                    "api_key": os.getenv("WEAVIATE_API_KEY", ""),
                    "class_name": "MemoryCollection"
                }
            }
        else:
            # 默认使用qdrant
            return {
                "provider": "qdrant",
                "config": {
                    "collection_name": "memory_collection",
                    "path": str(self.mem0_storage_dir / "qdrant_db"),
                    "embedding_model_dims": embedding_dims
                }
            }

    def get_mem0_config(self) -> Dict[str, Any]:
        """获取mem0配置"""
        return self.mem0_config

    def get_supported_vector_stores(self) -> Dict[str, str]:
        """获取支持的向量数据库列表"""
        return {
            "qdrant": "本地Qdrant数据库（推荐）",
            "chroma": "本地ChromaDB数据库",
            "faiss": "本地Faiss数据库（高性能）",
            "pinecone": "Pinecone云向量数据库",
            "weaviate": "Weaviate向量数据库"
        }
    
    def get_session_file_path(self, session_id: str) -> Path:
        """获取会话文件路径"""
        return self.sessions_dir / f"{session_id}.json"
    
    def update_llm_config(self, **kwargs):
        """更新LLM配置"""
        self.llm_config["config"].update(kwargs)

    def get_current_vector_store_info(self) -> Dict[str, str]:
        """获取当前使用的向量数据库信息"""
        supported_stores = self.get_supported_vector_stores()
        current_store = self.vector_store_type.lower()

        return {
            "type": current_store,
            "description": supported_stores.get(current_store, "未知向量数据库"),
            "config": self.mem0_config["vector_store"]["config"]
        }


# 全局配置实例
memory_config = MemoryConfig()
