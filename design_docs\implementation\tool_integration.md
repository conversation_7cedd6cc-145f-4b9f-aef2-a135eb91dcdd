# 🔧 工具集成设计

## 概述

工具集成系统基于AutoGen原生工具调用机制，为TrafficLogAgent和CodeExecutionAgent提供简化的工具注册和调用接口。系统采用Python函数定义工具，通过AutoGen的函数调用能力实现工具执行。

## 架构变更说明

**新架构特点**：
- 基于AutoGen原生工具调用，无需复杂的工具管理器
- 工具定义为标准Python函数，支持类型注解
- 简化的注册机制，减少中间层复杂性
- 直接的函数调用，提高执行效率

## 工具定义架构

### 工具函数定义规范
```python
from typing_extensions import Annotated
from typing import Dict, Any, List, Literal

async def tool_function_name(
    param1: Annotated[str, "参数1描述"],
    param2: Annotated[int, "参数2描述"] = default_value,
    param3: Annotated[Literal["option1", "option2"], "选项参数"] = "option1"
) -> Dict[str, Any]:
    """
    工具功能描述

    :param param1: 参数1说明
    :param param2: 参数2说明
    :param param3: 参数3说明
    :return: 执行结果字典
    """
    # 工具实现逻辑
    return {"result": "success", "data": {}}
```

### 工具分类（逻辑分类，无需枚举）
- **信息收集工具**: `query_traffic_logs` - OpenSearch流量日志查询
- **代码执行工具**: `execute_python_code`, `execute_ubuntu_command`
- **记忆管理工具**: `create_agent_memory_session`, `save_agent_interaction`, `search_agent_memories`, `get_agent_context`
- **自定义工具**: 根据需求扩展

## 工具注册机制

### Agent工具注册
```python
# 直接使用 BaseManagedAgent + 工具注册
from src.core.agents.base_managed_agent import BaseManagedAgent
from src.core.tools import get_all_tool_functions

# TrafficLogAgent - 流量日志查询工具
traffic_log_tools = get_information_collection_tool_functions() + get_memory_tool_functions()
traffic_agent = BaseManagedAgent(
    name="TrafficLogAgent",
    system_message=TRAFFIC_CONFIG["system_message"],
    model_client=model_client,
    context_provider=context_provider,
    agent_role=AgentRole.TOOL_EXECUTION,
    tools=traffic_log_tools
)

# CodeExecutionAgent - 代码执行工具
code_execution_tools = get_execution_tool_functions() + get_memory_tool_functions()
code_agent = BaseManagedAgent(
    name="CodeExecutionAgent",
    system_message=CODE_CONFIG["system_message"],
    model_client=model_client,
    context_provider=context_provider,
    agent_role=AgentRole.TOOL_EXECUTION,
    tools=code_execution_tools
)
```

### 工具函数集合管理
```python
def get_all_tool_functions() -> List[callable]:
    """获取所有工具函数"""
    tools = []
    tools.extend(get_memory_tool_functions())
    tools.extend(get_information_collection_tool_functions())
    tools.extend(get_execution_tool_functions())
    return tools
```

## 具体工具实现示例

### 网络扫描工具
```python
async def network_scan(
    target: Annotated[str, "目标IP或网段 (例如 '***********/24')"],
    ports: Annotated[str, "端口范围 (例如 '1-1000', '80,443')"] = "1-1000",
    scan_type: Annotated[Literal["tcp", "udp", "syn"], "扫描类型"] = "tcp"
) -> Dict[str, Any]:
    """
    扫描指定网络范围内的活跃主机和开放端口
    """
    print(f"🔍 [Tool] 开始网络扫描: {target}, 端口: {ports}, 类型: {scan_type}")

    # 模拟扫描过程
    await asyncio.sleep(random.uniform(1, 3))

    # 模拟扫描结果
    hosts = []
    for i in range(random.randint(1, 5)):
        host_ip = f"192.168.1.{random.randint(1, 254)}"
        open_ports = [random.randint(20, 9999) for _ in range(random.randint(1, 5))]

        hosts.append({
            "ip": host_ip,
            "status": "up",
            "open_ports": open_ports,
            "os_guess": random.choice(["Linux", "Windows", "Unknown"]),
            "response_time": f"{random.randint(1, 100)}ms"
        })

    return {
        "hosts": hosts,
        "scan_time": datetime.now().isoformat(),
        "total_hosts": len(hosts),
        "scan_parameters": {
            "target": target,
            "ports": ports,
            "scan_type": scan_type
        }
    }
```

### 威胁情报查询工具
```python
async def threat_intel(
    indicator: Annotated[str, "要查询的指标 (IP, 域名, 哈希等)"],
    indicator_type: Annotated[Literal["ip", "domain", "hash", "url"], "指标类型"],
    sources: Annotated[List[str], "要查询的情报源列表"] = None
) -> Dict[str, Any]:
    """
    查询IP、域名、文件哈希的威胁情报信息
    """
    if sources is None:
        sources = ["virustotal", "alienvault", "threatcrowd"]

    print(f"🔍 [Tool] 查询威胁情报: {indicator} (类型: {indicator_type})")

    # 模拟查询过程
    await asyncio.sleep(random.uniform(0.5, 1.5))

    # 模拟威胁情报结果
    is_malicious = random.choice([True, False, False])  # 33%概率为恶意

    intel_result = {
        "indicator": indicator,
        "indicator_type": indicator_type,
        "is_malicious": is_malicious,
        "confidence": random.randint(60, 95) if is_malicious else random.randint(10, 40),
        "sources_checked": sources,
        "query_time": datetime.now().isoformat()
    }

    if is_malicious:
        intel_result.update({
            "threat_types": random.sample(["malware", "phishing", "botnet", "c2"], random.randint(1, 3)),
            "first_seen": "2024-01-15T10:30:00Z",
            "last_seen": datetime.now().isoformat(),
            "reputation_score": random.randint(1, 30)
        })
    else:
        intel_result.update({
            "reputation_score": random.randint(70, 100)
        })

    return intel_result
```

## 工具扩展机制

### 添加新工具
1. **定义工具函数**：按照标准格式定义异步函数
2. **添加到工具列表**：在 `get_default_tool_functions()` 中注册
3. **更新Agent配置**：如需要，更新Agent的系统消息

### 工具函数最佳实践
```python
async def new_security_tool(
    # 使用Annotated类型注解提供参数描述
    param1: Annotated[str, "参数描述"],
    param2: Annotated[int, "数值参数"] = 100,
    param3: Annotated[Literal["option1", "option2"], "选项参数"] = "option1"
) -> Dict[str, Any]:
    """
    工具功能的详细描述

    :param param1: 参数1的详细说明
    :param param2: 参数2的详细说明
    :param param3: 参数3的详细说明
    :return: 标准化的结果字典
    """
    try:
        # 参数验证
        if not param1:
            raise ValueError("param1不能为空")

        # 工具执行逻辑
        result = await execute_tool_logic(param1, param2, param3)

        # 返回标准化结果
        return {
            "status": "success",
            "data": result,
            "timestamp": datetime.now().isoformat(),
            "tool_name": "new_security_tool"
        }

    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "new_security_tool"
        }
```

## 错误处理和安全考虑

### 工具函数错误处理
```python
async def secure_tool_template(
    param: Annotated[str, "参数描述"]
) -> Dict[str, Any]:
    """安全的工具函数模板"""
    try:
        # 输入验证
        if not param or not isinstance(param, str):
            raise ValueError("参数验证失败")

        # 输入清理（移除危险字符）
        clean_param = re.sub(r'[;&|`$]', '', param)

        # 工具执行逻辑
        result = await execute_tool_logic(clean_param)

        return {
            "status": "success",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        # 错误日志记录
        print(f"❌ 工具执行失败: {str(e)}")

        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat()
        }
```

### 工具执行监控
```python
# 简化的执行日志
def log_tool_execution(tool_name: str, params: Dict[str, Any], result: Dict[str, Any]):
    """记录工具执行日志"""
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "tool": tool_name,
        "status": result.get("status", "unknown"),
        "execution_time": result.get("execution_time", 0)
    }
    print(f"📊 [Tool Log] {log_entry}")
```

## 架构优势

### 简化的工具架构优势
1. **减少复杂性**: 无需复杂的工具管理器和权限系统
2. **提高性能**: 直接函数调用，减少中间层开销
3. **易于维护**: 工具定义清晰，易于理解和修改
4. **快速扩展**: 添加新工具只需定义函数并注册
5. **类型安全**: 使用类型注解提供更好的IDE支持和错误检查

### 与传统架构的对比

| 特性 | 传统复杂架构 | 新简化架构 |
|------|-------------|-----------|
| 工具定义 | 复杂的类继承体系 | 简单的Python函数 |
| 权限管理 | 复杂的权限控制系统 | 基于Agent配置的简单控制 |
| 执行方式 | 多层调用链 | 直接函数调用 |
| 扩展性 | 需要实现多个接口 | 只需定义函数 |
| 维护成本 | 高 | 低 |
| 性能开销 | 高 | 低 |

## 实施指南

### 快速开始步骤
1. **定义工具函数**: 按照标准格式创建异步函数
2. **添加类型注解**: 使用 `Annotated` 提供参数描述
3. **注册到Agent**: 在 `get_default_tool_functions()` 中添加
4. **测试工具**: 验证工具函数的正确性

### 开发工作流
```bash
# 1. 创建新工具文件
touch src/core/tools/new_tool.py

# 2. 定义工具函数
# 3. 添加到工具列表
# 4. 测试工具功能
python -m pytest tests/test_tools.py
```

## 总结

### 新架构的核心特点
1. **简化设计**: 基于AutoGen原生工具调用，无需复杂的管理层
2. **类型安全**: 使用Python类型注解提供更好的开发体验
3. **易于扩展**: 添加新工具只需定义函数并注册
4. **高性能**: 直接函数调用，减少中间层开销
5. **易维护**: 清晰的代码结构，便于理解和修改

### 适用场景
- 需要快速集成安全工具的场景
- 对性能有较高要求的实时分析
- 团队规模较小，需要简化架构的项目
- 原型开发和快速迭代

### 未来扩展方向
- 支持更多类型的安全工具
- 增加工具执行的监控和统计
- 实现工具结果的缓存机制
- 支持工具的版本管理和热更新

---

**文档更新**: 2024-12-19
**架构版本**: 简化版 v2.0
**兼容性**: AutoGen v0.2+
