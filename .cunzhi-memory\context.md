# 项目上下文信息

- 用户LLM和embedding接口信息：LLM接口http://***********:18089/v1/chat/completions，模型secllm-v3，需要Bearer认证；embedding接口http://*************:5000/api/v1/ml/similarity/embeddings，模型bce-embedding-base-v1；对话存储结构调整为单个message而非messages列表，包含role,content,reasoning,tool_call,tool_response等字段
- 新增信息收集工具集 src/tools/information_collection_tools.py，提供OpenSearch流量日志查询功能，包括基础查询、IP搜索、协议过滤、威胁指标搜索、时间范围分析和高级流量分析等6个工具函数。连接配置：************:9200，认证admin/Open-1024，索引arkime*。查询结果自动保存到data/query_results/目录。已更新requirements.txt添加opensearch-py依赖，已更新tools/__init__.py集成新工具。
- 新增TrafficLogAgent流量日志分析专家，配置文件src/agent_settings/traffic_log_agent.py，专门负责OpenSearch中流量日志的检索、查询、过滤、筛选、聚合和分析。已集成到EnhancedGroupChatManager中，使用TOOL_EXECUTION角色，加载信息收集工具和记忆管理工具。Agent具备6个专门的流量分析工具，支持多维度数据过滤、流量模式识别、数据聚合分析等功能。
- 用户要求新增威胁情报查询工具集和对应的Agent，基于提供的参考代码但简化复杂解析规则，使用prompt让模型提供符合要求的参数，支持IP、域名、CVE漏洞、文件hash、URL、APT组织查询，使用NTI接口
- 已成功创建威胁情报查询工具集和ThreatIntelligenceAgent，包含7个威胁情报查询工具(IP、域名、URL、文件哈希、CVE、APT、通用查询)，使用NTI接口，支持自动指标识别，已集成到EnhancedGroupChatManager中，所有测试通过
- 成功创建了Agent系统前端可视化界面，位于simulation_ui/web目录，使用React+TypeScript+React Flow+Tailwind CSS技术栈，实现了真实Agent系统控制台界面，包含用户输入、Agent状态监控、流程可视化、系统监控和实时日志功能，能够读取simulation_data中的真实数据并动态展示Agent执行过程
