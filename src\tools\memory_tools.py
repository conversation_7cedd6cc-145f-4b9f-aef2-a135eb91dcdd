"""
记忆管理工具函数集合

为Agent系统提供记忆管理功能，替换原有的MemoryAgent和memory_manager
基于新的记忆管理系统，提供标准化的工具函数接口
"""

from typing import Dict, Any, List, Optional, Callable
from typing_extensions import Annotated
from datetime import datetime
import json

from src.extensions.memory import MemoryTools
from src.extensions.memory.integration_adapter import MemoryIntegrationAdapter
from src.utils.logger import get_logger


# 全局记忆管理实例
_memory_tools = MemoryTools()
_memory_adapter = MemoryIntegrationAdapter()
_logger = get_logger("memory_tools")


async def create_agent_memory_session(
    agent_id: Annotated[str, "Agent ID，用于标识不同的Agent"],
    description: Annotated[str, "会话描述，说明这个会话的用途"],
    metadata: Annotated[Optional[str], "可选的JSON格式元数据"] = None
) -> Dict[str, Any]:
    """
    为Agent创建专用记忆会话
    
    每个Agent都应该有自己的记忆会话来管理对话历史和上下文
    
    :param agent_id: Agent的唯一标识符
    :param description: 会话的描述信息
    :param metadata: 可选的元数据，JSON字符串格式
    :return: 包含会话ID和创建结果的字典
    """
    try:
        # 解析元数据
        parsed_metadata = None
        if metadata:
            try:
                parsed_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                return {
                    "status": "error",
                    "error_message": "元数据格式错误，必须是有效的JSON字符串",
                    "timestamp": datetime.now().isoformat(),
                    "tool_name": "create_agent_memory_session"
                }
        
        # 创建Agent会话
        session_id = _memory_adapter.create_agent_session(agent_id, description, parsed_metadata)
        
        return {
            "status": "success",
            "data": {
                "session_id": session_id,
                "agent_id": agent_id,
                "description": description
            },
            "message": f"成功为Agent {agent_id} 创建记忆会话: {session_id}",
            "timestamp": datetime.now().isoformat(),
            "tool_name": "create_agent_memory_session"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "create_agent_memory_session"
        }


async def save_agent_interaction(
    agent_id: Annotated[str, "Agent ID"],
    user_input: Annotated[str, "用户输入内容"],
    agent_response: Annotated[str, "Agent响应内容"],
    reasoning: Annotated[Optional[str], "Agent的推理过程"] = None,
    tool_calls: Annotated[Optional[str], "工具调用信息，JSON格式"] = None,
    tool_responses: Annotated[Optional[str], "工具响应信息，JSON格式"] = None
) -> Dict[str, Any]:
    """
    保存Agent与用户的交互记录
    
    将完整的对话交互保存到Agent的记忆会话中，包括推理过程和工具调用
    
    :param agent_id: Agent ID
    :param user_input: 用户的输入内容
    :param agent_response: Agent的响应内容
    :param reasoning: Agent的推理过程（可选）
    :param tool_calls: 工具调用信息，JSON格式（可选）
    :param tool_responses: 工具响应信息，JSON格式（可选）
    :return: 保存结果
    """
    try:
        # 解析工具调用信息
        parsed_tool_calls = None
        parsed_tool_responses = None
        
        if tool_calls:
            try:
                parsed_tool_calls = json.loads(tool_calls)
            except json.JSONDecodeError:
                return {
                    "status": "error",
                    "error_message": "工具调用信息格式错误，必须是有效的JSON",
                    "timestamp": datetime.now().isoformat(),
                    "tool_name": "save_agent_interaction"
                }
        
        if tool_responses:
            try:
                parsed_tool_responses = json.loads(tool_responses)
            except json.JSONDecodeError:
                return {
                    "status": "error",
                    "error_message": "工具响应信息格式错误，必须是有效的JSON",
                    "timestamp": datetime.now().isoformat(),
                    "tool_name": "save_agent_interaction"
                }
        
        # 保存交互记录
        success = _memory_adapter.save_agent_interaction(
            agent_id=agent_id,
            user_input=user_input,
            agent_response=agent_response,
            reasoning=reasoning,
            tool_calls=parsed_tool_calls if parsed_tool_calls else None,
            tool_responses=parsed_tool_responses if parsed_tool_responses else None
        )
        
        if success:
            return {
                "status": "success",
                "data": {
                    "agent_id": agent_id,
                    "interaction_saved": True
                },
                "message": f"成功保存Agent {agent_id} 的交互记录",
                "timestamp": datetime.now().isoformat(),
                "tool_name": "save_agent_interaction"
            }
        else:
            return {
                "status": "error",
                "error_message": "保存交互记录失败",
                "timestamp": datetime.now().isoformat(),
                "tool_name": "save_agent_interaction"
            }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "save_agent_interaction"
        }


async def search_agent_memories(
    agent_id: Annotated[str, "Agent ID"],
    query: Annotated[str, "搜索查询内容"],
    include_global: Annotated[bool, "是否包含全局记忆搜索"] = False,
    limit: Annotated[int, "返回结果数量限制"] = 10
) -> Dict[str, Any]:
    """
    搜索Agent的相关记忆
    
    基于语义搜索在Agent的记忆中查找相关内容
    
    :param agent_id: Agent ID
    :param query: 搜索查询内容
    :param include_global: 是否包含全局记忆搜索
    :param limit: 返回结果数量限制
    :return: 搜索结果
    """
    try:
        memories = _memory_adapter.retrieve_agent_memories(
            agent_id=agent_id,
            query=query,
            include_global=include_global,
            limit=limit
        )
        
        return {
            "status": "success",
            "data": {
                "agent_id": agent_id,
                "query": query,
                "total_results": len(memories),
                "memories": memories
            },
            "message": f"为Agent {agent_id} 找到 {len(memories)} 条相关记忆",
            "timestamp": datetime.now().isoformat(),
            "tool_name": "search_agent_memories"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "search_agent_memories"
        }


async def get_agent_context(
    agent_id: Annotated[str, "Agent ID"],
    current_query: Annotated[str, "当前查询内容"],
    max_length: Annotated[int, "最大上下文长度"] = 2000
) -> Dict[str, Any]:
    """
    获取Agent的格式化上下文信息
    
    为Agent提供相关的历史上下文，帮助理解当前任务
    
    :param agent_id: Agent ID
    :param current_query: 当前的查询或任务内容
    :param max_length: 最大上下文长度
    :return: 格式化的上下文信息
    """
    try:
        context = _memory_adapter.format_context_for_agent(
            agent_id=agent_id,
            query=current_query,
            max_context_length=max_length
        )
        
        return {
            "status": "success",
            "data": {
                "agent_id": agent_id,
                "context": context,
                "context_length": len(context)
            },
            "message": f"成功获取Agent {agent_id} 的上下文信息",
            "timestamp": datetime.now().isoformat(),
            "tool_name": "get_agent_context"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "get_agent_context"
        }


async def store_analysis_case(
    user_request: Annotated[str, "用户的原始请求"],
    analysis_plan: Annotated[str, "分析计划内容"],
    execution_results: Annotated[str, "执行结果，JSON格式"],
    final_report: Annotated[str, "最终分析报告"]
) -> Dict[str, Any]:
    """
    存储完整的分析案例到长期记忆
    
    将完整的分析过程作为案例存储，供未来参考
    
    :param user_request: 用户的原始请求
    :param analysis_plan: 制定的分析计划
    :param execution_results: 执行结果，JSON格式
    :param final_report: 最终的分析报告
    :return: 存储结果
    """
    try:
        # 解析执行结果
        try:
            parsed_results = json.loads(execution_results)
        except json.JSONDecodeError:
            return {
                "status": "error",
                "error_message": "执行结果格式错误，必须是有效的JSON",
                "timestamp": datetime.now().isoformat(),
                "tool_name": "store_analysis_case"
            }
        
        # 存储分析案例
        session_id = _memory_adapter.memory_tools.store_analysis_case(
            user_request=user_request,
            analysis_plan=analysis_plan,
            execution_results=parsed_results,
            final_report=final_report
        )
        
        return {
            "status": "success",
            "data": {
                "session_id": session_id,
                "case_stored": True
            },
            "message": f"成功存储分析案例到会话: {session_id}",
            "timestamp": datetime.now().isoformat(),
            "tool_name": "store_analysis_case"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "store_analysis_case"
        }


async def search_global_memories(
    query: Annotated[str, "搜索查询内容"],
    limit: Annotated[int, "返回结果数量限制"] = 10
) -> Dict[str, Any]:
    """
    在全局范围内搜索记忆
    
    跨所有会话和Agent搜索相关记忆内容
    
    :param query: 搜索查询内容
    :param limit: 返回结果数量限制
    :return: 搜索结果
    """
    try:
        result = _memory_tools.retrieve_memories(
            query=query,
            session_id=None,
            global_search=True,
            limit=limit
        )
        
        if result['success']:
            memories = result['memories']
            return {
                "status": "success",
                "data": {
                    "query": query,
                    "total_results": len(memories),
                    "memories": memories
                },
                "message": f"全局搜索找到 {len(memories)} 条相关记忆",
                "timestamp": datetime.now().isoformat(),
                "tool_name": "search_global_memories"
            }
        else:
            return {
                "status": "error",
                "error_message": result.get('error', '搜索失败'),
                "timestamp": datetime.now().isoformat(),
                "tool_name": "search_global_memories"
            }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "tool_name": "search_global_memories"
        }


# 工具函数列表
def get_memory_tool_functions() -> List[Callable]:
    """获取所有记忆管理工具函数"""
    return [
        create_agent_memory_session,
        save_agent_interaction,
        search_agent_memories,
        get_agent_context,
        store_analysis_case,
        search_global_memories
    ]
