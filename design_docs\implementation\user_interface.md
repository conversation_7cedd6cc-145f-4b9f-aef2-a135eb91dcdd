# 🖥️ 用户界面设计

## 概述

用户界面系统采用抽象化设计，提供统一的交互接口，支持多种前端类型的无缝切换和扩展。

## 界面架构

### 抽象层设计
```python
class BaseUserInterface(ABC):
    """用户界面抽象基类"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化界面"""
        pass
    
    @abstractmethod
    async def display_message(self, message: UIMessage):
        """显示消息"""
        pass
    
    @abstractmethod
    async def get_user_input(self, input_request: UIInput) -> Optional[str]:
        """获取用户输入"""
        pass
    
    @abstractmethod
    async def show_progress(self, progress: ProgressInfo):
        """显示进度"""
        pass
```

### 界面类型枚举
```python
class UIType(Enum):
    CLI = "cli"         # 命令行界面
    WEB = "web"         # Web界面
    GUI = "gui"         # 图形界面
    API = "api"         # API接口
```

## 消息系统

### 消息类型定义
```python
class MessageType(Enum):
    INFO = "info"           # 信息消息
    SUCCESS = "success"     # 成功消息
    WARNING = "warning"     # 警告消息
    ERROR = "error"         # 错误消息
    QUESTION = "question"   # 询问消息
    PROGRESS = "progress"   # 进度消息
```

### 消息数据结构
```python
@dataclass
class UIMessage:
    message_type: MessageType
    content: str
    title: Optional[str] = None
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
```

### 输入类型定义
```python
class InputType(Enum):
    TEXT = "text"           # 文本输入
    CHOICE = "choice"       # 选择输入
    CONFIRM = "confirm"     # 确认输入
    FILE = "file"           # 文件输入
    PASSWORD = "password"   # 密码输入
```

## CLI界面实现

### CLIUserInterface特性
```python
class CLIUserInterface(BaseUserInterface):
    def __init__(self):
        super().__init__(UIType.CLI)
        self.colors = {
            MessageType.INFO: "\033[94m",      # 蓝色
            MessageType.SUCCESS: "\033[92m",   # 绿色
            MessageType.WARNING: "\033[93m",   # 黄色
            MessageType.ERROR: "\033[91m",     # 红色
            MessageType.QUESTION: "\033[96m",  # 青色
        }
        self.icons = {
            MessageType.INFO: "ℹ️",
            MessageType.SUCCESS: "✅",
            MessageType.WARNING: "⚠️",
            MessageType.ERROR: "❌",
            MessageType.QUESTION: "❓",
            MessageType.PROGRESS: "🔄"
        }
```

### 消息显示格式
```python
async def display_message(self, message: UIMessage):
    """显示消息到CLI"""
    color = self.colors.get(message.message_type, "")
    icon = self.icons.get(message.message_type, "")
    reset = self.reset_color
    
    timestamp = message.timestamp.strftime("%H:%M:%S")
    
    if message.title:
        print(f"{color}{icon} [{timestamp}] {message.title}: {message.content}{reset}")
    else:
        print(f"{color}{icon} [{timestamp}] {message.content}{reset}")
```

### 用户输入处理
```python
async def get_user_input(self, input_request: UIInput) -> Optional[str]:
    """获取CLI用户输入"""
    if input_request.input_type == InputType.TEXT:
        prompt = f"❓ {input_request.prompt}"
        if input_request.default_value:
            prompt += f" (默认: {input_request.default_value})"
        prompt += ": "
        
        user_input = input(prompt).strip()
        if not user_input and input_request.default_value:
            user_input = input_request.default_value
        
        return user_input
    
    elif input_request.input_type == InputType.CHOICE:
        print(f"❓ {input_request.prompt}")
        for i, choice in enumerate(input_request.choices, 1):
            marker = " (默认)" if choice == input_request.default_value else ""
            print(f"  {i}. {choice}{marker}")
        
        # 处理选择逻辑
        return self._handle_choice_input(input_request)
```

### 进度显示
```python
async def show_progress(self, progress: ProgressInfo):
    """显示CLI进度"""
    bar_length = 30
    filled_length = int(bar_length * progress.current // progress.total)
    bar = "█" * filled_length + "░" * (bar_length - filled_length)
    
    print(f"\r🔄 {progress.message} [{bar}] {progress.percentage:.1f}% ({progress.current}/{progress.total})", 
          end="", flush=True)
    
    if progress.current >= progress.total:
        print()  # 换行
```

## Web界面设计 (预留)

### Web界面架构
```python
class WebUserInterface(BaseUserInterface):
    """Web用户界面实现"""
    
    def __init__(self):
        super().__init__(UIType.WEB)
        self.app = None  # Web应用实例
        self.websocket_connections = []
        self.message_queue = asyncio.Queue()
    
    async def initialize(self) -> bool:
        """初始化Web界面"""
        # 初始化Web服务器
        # 设置WebSocket连接
        # 配置静态资源
        pass
    
    async def display_message(self, message: UIMessage):
        """通过WebSocket发送消息"""
        message_data = {
            "type": "message",
            "data": {
                "message_type": message.message_type.value,
                "content": message.content,
                "title": message.title,
                "timestamp": message.timestamp.isoformat()
            }
        }
        await self._broadcast_to_clients(message_data)
```

### WebSocket消息协议
```json
{
    "type": "message|input_request|progress|result",
    "data": {
        "message_type": "info|success|warning|error",
        "content": "消息内容",
        "title": "消息标题",
        "timestamp": "2024-12-19T10:30:00Z"
    }
}
```

### 前端界面组件
```javascript
// React组件示例
const MessageDisplay = ({ message }) => {
    const getIcon = (type) => {
        const icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        };
        return icons[type] || '';
    };
    
    return (
        <div className={`message message-${message.message_type}`}>
            <span className="icon">{getIcon(message.message_type)}</span>
            <span className="timestamp">{formatTime(message.timestamp)}</span>
            <span className="content">{message.content}</span>
        </div>
    );
};
```

## GUI界面设计 (预留)

### GUI框架选择
- **Tkinter**: Python内置GUI库
- **PyQt/PySide**: 功能强大的跨平台GUI框架
- **Kivy**: 现代化的多点触控GUI框架
- **Dear PyGui**: 高性能的现代GUI框架

### GUI界面布局
```python
class GUIUserInterface(BaseUserInterface):
    """GUI用户界面实现"""
    
    def __init__(self):
        super().__init__(UIType.GUI)
        self.window = None
        self.message_area = None
        self.input_area = None
        self.progress_bar = None
    
    async def initialize(self) -> bool:
        """初始化GUI界面"""
        # 创建主窗口
        # 设置布局
        # 初始化组件
        pass
    
    def create_main_layout(self):
        """创建主界面布局"""
        # 顶部：标题栏
        # 中间：消息显示区域
        # 底部：输入区域和控制按钮
        # 侧边：进度显示和状态信息
        pass
```

## API接口设计 (预留)

### RESTful API接口
```python
class APIUserInterface(BaseUserInterface):
    """API用户界面实现"""
    
    def __init__(self):
        super().__init__(UIType.API)
        self.api_server = None
        self.active_sessions = {}
    
    async def initialize(self) -> bool:
        """初始化API服务"""
        # 启动FastAPI服务器
        # 配置路由
        # 设置认证
        pass
```

### API端点定义
```python
from fastapi import FastAPI, WebSocket

app = FastAPI(title="Security Analysis API")

@app.post("/analysis/start")
async def start_analysis(request: AnalysisRequest):
    """开始分析任务"""
    pass

@app.get("/analysis/{session_id}/status")
async def get_analysis_status(session_id: str):
    """获取分析状态"""
    pass

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket实时通信"""
    pass
```

## 界面管理器

### UIManager核心功能
```python
class UIManager:
    """用户界面管理器"""
    
    def __init__(self):
        self.interfaces: Dict[UIType, BaseUserInterface] = {}
        self.current_interface: Optional[BaseUserInterface] = None
        self.default_ui_type = UIType.CLI
    
    def register_interface(self, interface: BaseUserInterface):
        """注册用户界面"""
        self.interfaces[interface.ui_type] = interface
    
    async def set_active_interface(self, ui_type: UIType) -> bool:
        """设置活跃界面"""
        if ui_type not in self.interfaces:
            return False
        
        # 清理当前界面
        if self.current_interface:
            await self.current_interface.cleanup()
        
        # 初始化新界面
        interface = self.interfaces[ui_type]
        if await interface.initialize():
            self.current_interface = interface
            return True
        
        return False
```

### 界面切换机制
```python
async def switch_interface(self, new_ui_type: UIType, 
                          preserve_session: bool = True) -> bool:
    """切换用户界面"""
    if preserve_session and self.current_interface:
        # 保存当前会话状态
        session_state = await self.current_interface.get_session_state()
    
    # 切换界面
    success = await self.set_active_interface(new_ui_type)
    
    if success and preserve_session:
        # 恢复会话状态
        await self.current_interface.restore_session_state(session_state)
    
    return success
```

## 响应式设计

### 自适应布局
```python
class ResponsiveLayout:
    """响应式布局管理"""
    
    def __init__(self):
        self.breakpoints = {
            'mobile': 768,
            'tablet': 1024,
            'desktop': 1440
        }
    
    def get_layout_config(self, screen_width: int) -> Dict[str, Any]:
        """根据屏幕宽度获取布局配置"""
        if screen_width < self.breakpoints['mobile']:
            return self.mobile_layout
        elif screen_width < self.breakpoints['tablet']:
            return self.tablet_layout
        else:
            return self.desktop_layout
```

### 主题系统
```python
class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.themes = {
            'light': LightTheme(),
            'dark': DarkTheme(),
            'high_contrast': HighContrastTheme()
        }
        self.current_theme = 'light'
    
    def apply_theme(self, theme_name: str):
        """应用主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            theme = self.themes[theme_name]
            theme.apply_to_interface(self.current_interface)
```

## 国际化支持

### 多语言支持
```python
class I18nManager:
    """国际化管理器"""
    
    def __init__(self):
        self.translations = {}
        self.current_locale = 'zh_CN'
    
    def load_translations(self, locale: str):
        """加载翻译文件"""
        translation_file = f"locales/{locale}.json"
        with open(translation_file, 'r', encoding='utf-8') as f:
            self.translations[locale] = json.load(f)
    
    def translate(self, key: str, **kwargs) -> str:
        """翻译文本"""
        translation = self.translations.get(self.current_locale, {}).get(key, key)
        return translation.format(**kwargs)
```

### 翻译文件示例
```json
{
    "zh_CN": {
        "analysis.start": "开始分析",
        "analysis.complete": "分析完成",
        "error.network": "网络连接错误",
        "progress.scanning": "正在扫描 {target}..."
    },
    "en_US": {
        "analysis.start": "Start Analysis",
        "analysis.complete": "Analysis Complete",
        "error.network": "Network Connection Error",
        "progress.scanning": "Scanning {target}..."
    }
}
```

## 可访问性支持

### 无障碍设计
```python
class AccessibilityManager:
    """可访问性管理器"""
    
    def __init__(self):
        self.screen_reader_enabled = False
        self.high_contrast_mode = False
        self.font_size_multiplier = 1.0
    
    def enable_screen_reader_support(self):
        """启用屏幕阅读器支持"""
        self.screen_reader_enabled = True
        # 添加ARIA标签
        # 提供键盘导航
        # 增强语义化
    
    def set_high_contrast_mode(self, enabled: bool):
        """设置高对比度模式"""
        self.high_contrast_mode = enabled
        # 调整颜色方案
        # 增强视觉对比度
```

## 性能优化

### 虚拟化渲染
```python
class VirtualRenderer:
    """虚拟化渲染器"""
    
    def __init__(self, viewport_size: int = 100):
        self.viewport_size = viewport_size
        self.total_items = 0
        self.visible_range = (0, viewport_size)
    
    def render_visible_items(self, items: List[Any]) -> List[Any]:
        """只渲染可见项目"""
        start, end = self.visible_range
        return items[start:end]
```

### 消息缓冲
```python
class MessageBuffer:
    """消息缓冲器"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.messages = deque(maxlen=max_size)
        self.flush_interval = 0.1  # 100ms
    
    async def add_message(self, message: UIMessage):
        """添加消息到缓冲区"""
        self.messages.append(message)
        
        # 批量刷新
        if len(self.messages) >= 10:
            await self.flush_messages()
```

## 最佳实践

### 用户体验原则
1. **一致性**: 保持界面元素和交互的一致性
2. **反馈性**: 及时提供操作反馈和状态信息
3. **容错性**: 提供错误恢复和撤销机制
4. **效率性**: 优化常用操作的便捷性

### 界面设计指南
1. **简洁明了**: 避免界面过于复杂
2. **信息层次**: 合理组织信息的层次结构
3. **视觉引导**: 使用颜色和图标引导用户注意力
4. **响应式设计**: 适配不同屏幕尺寸和设备

### 开发建议
1. **模块化设计**: 组件化的界面开发
2. **状态管理**: 统一的状态管理机制
3. **测试覆盖**: 完善的界面测试
4. **文档完善**: 详细的使用文档和API文档
