"""
上下文提供者 - 负责为Agent构建统一的单轮对话上下文

从BaseManagedAgent中移出上下文组织逻辑，统一在此处理。
支持主流程和子流程的不同上下文格式。
"""

from typing import Dict, Any, Optional
from src.extensions.memory.integration_adapter import MemoryIntegrationAdapter
from src.utils.logger import get_logger


class SingleTurnContextProvider:
    """
    单轮对话上下文提供者

    负责为Agent构建统一的上下文，包括：
    - 主流程Agent上下文（任务清单 + 历史执行结果）
    - 子流程Agent上下文（当前任务 + 对话历史 + 摘要）
    - Agent个人记忆上下文
    """

    def __init__(self):
        self.memory_adapter = MemoryIntegrationAdapter()
        self.logger = get_logger("context_provider")

    def build_main_flow_context(self, context_data: Dict[str, Any], current_request: str) -> str:
        """构建主流程Agent的用户消息"""
        session_info = context_data.get('session_info', {})
        task_list_markdown = context_data.get('task_list_markdown', '')
        execution_history = context_data.get('execution_history', [])

        # 获取分析进度信息
        analysis_progress = session_info.get('analysis_progress', {})

        # 构建执行历史部分
        history_text = "暂无历史执行结果"
        if execution_history:
            history_items = []
            for i, result in enumerate(execution_history, 1):
                task_info = result.get('task', '未知任务')
                status = result.get('status', '未知状态')
                summary = str(result.get('summary', result.get('result', '无摘要')))[:200]
                history_items.append(f"{i}. 任务: {task_info}")
                history_items.append(f"   状态: {status}")
                history_items.append(f"   摘要: {summary}")
                history_items.append("")
            history_text = "\n".join(history_items)

        return f"""# 会话信息
用户请求: {session_info.get('user_request', '未知请求')}
分析计划: {session_info.get('analysis_plan', '暂无计划')}

⚠️ 分析进度控制: {analysis_progress.get('progress_message', '分析进度未知')}
剩余分析次数: {analysis_progress.get('remaining', '未知')}次

# 任务清单
{task_list_markdown if task_list_markdown else '暂无任务清单'}

# 历史执行结果
{history_text}

# 当前请求
{current_request}"""

    def build_sub_flow_context(self, context_data: Dict[str, Any], current_request: str) -> str:
        """构建子流程Agent的用户消息"""
        task_info = context_data.get('task_info', {})
        activity_history = context_data.get('activity_history', [])
        context_summary = context_data.get('context_summary', '')



        # 构建当前任务信息（包含分析进度）
        analysis_progress = task_info.get('analysis_progress', {})
        task_text = f"""任务ID: {task_info.get('task_id', '未知')}
任务标题: {task_info.get('task_title', '未知任务')}

⚠️ 分析进度控制: {analysis_progress.get('progress_message', '分析进度未知')}
剩余分析次数: {analysis_progress.get('remaining', '未知')}次"""

        # 构建统一活动历史（保持时间顺序）
        history_text = "暂无活动历史"
        if activity_history:
            history_items = []
            for activity in activity_history:
                agent_name = activity.get('agent_name', '未知Agent')
                agent_role = activity.get('agent_role', '未知角色')
                message_role = activity.get('role', 'assistant')  # 使用统一的role字段
                sequence = activity.get('analysis_sequence', 0)
                timestamp = activity.get('timestamp', '')

                # 格式化时间戳
                time_str = timestamp.split('T')[1][:8] if 'T' in timestamp else timestamp

                # 如果是摘要版本，使用摘要
                if activity.get('is_summarized', False):
                    summary = activity.get('message_summary', '无摘要')
                    history_items.append(f"- [{time_str}] {agent_name}({agent_role}) | {message_role}")
                    history_items.append(f"  摘要: {summary}")
                else:
                    # 完整版本，但限制长度
                    content = activity.get('content', '')[:100]
                    history_items.append(f"- [{time_str}] {agent_name}({agent_role}) | {message_role}")
                    if content:
                        history_items.append(f"  内容: {content}...")

                    # 显示工具调用信息
                    if activity.get('tool_call'):
                        tool_info = activity['tool_call']
                        if 'name' in tool_info:
                            history_items.append(f"  工具: {tool_info['name']}")
                        elif 'calls' in tool_info:
                            tool_names = [call.get('name', '未知') for call in tool_info['calls']]
                            history_items.append(f"  工具: {', '.join(tool_names)}")

                history_items.append("")
            history_text = "\n".join(history_items)

        return f"""# 当前任务
{task_text}

# 活动历史（按时间顺序）
{history_text}

# 上下文摘要
{context_summary if context_summary else '暂无摘要'}

# 当前请求
{current_request}"""

    def get_agent_memory_context(self, agent_name: str, current_query: str) -> str:
        """
        获取Agent个人记忆上下文

        Args:
            agent_name: Agent名称
            current_query: 当前查询内容

        Returns:
            str: 格式化的记忆上下文
        """
        try:
            # 搜索相关记忆
            memories = self.memory_adapter.retrieve_agent_memories(
                agent_id=agent_name,
                query=current_query,
                include_global=False,  # 只获取Agent个人记忆
                limit=3  # 限制数量避免上下文过长
            )

            if not memories:
                return "暂无相关个人记忆。"

            memory_parts = []
            for i, memory in enumerate(memories, 1):
                content = memory.get('content', '')
                relevance = memory.get('relevance_score', 0.0)
                timestamp = memory.get('timestamp', '')

                memory_parts.append(f"{i}. [相关度: {relevance:.2f}] {content[:200]}...")
                if timestamp:
                    memory_parts.append(f"   时间: {timestamp}")
                memory_parts.append("")

            return "\n".join(memory_parts)

        except Exception as e:
            self.logger.warning(f"⚠️ 获取Agent记忆失败: {e}")
            return "记忆获取失败，将基于当前上下文进行处理。"

