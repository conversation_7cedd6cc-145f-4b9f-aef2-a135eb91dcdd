"""
包含托管Agent的基类，该Agent可以自动处理上下文。
"""

from typing import List, Any, Optional, AsyncIterator, Dict
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken

from src.core.context_provider import SingleTurnContextProvider
from src.core.task_manager import TaskItem
from src.agent_settings.agent_roles import Agent<PERSON><PERSON>
from src.utils.logger import get_logger, print_llm_interaction

class BaseManagedAgent(AssistantAgent):
    """
    带上下文自动管理的Agent基类
    """

    def __init__(self,
                 name: str,
                 context_provider: SingleTurnContextProvider,
                 agent_role: AgentRole = AgentRole.GENERAL,
                 **kwargs):
        super().__init__(name=name, **kwargs)
        self.context_provider = context_provider
        self.agent_role = agent_role
        self.logger = get_logger(f"agent_{name}")

        # 存储system message（可以从配置中获取）
        self._system_message = kwargs.get('system_message', f"你是一个{agent_role.value}Agent，专门负责相关任务的处理。")

        # 存储当前的任务管理器引用，用于处理autogen内部调用
        self._current_main_task_manager = None
        self._current_task = None

    def _get_system_message(self) -> str:
        """获取Agent的system message"""
        return self._system_message



    def _inject_context_to_messages(self,
                                   messages: List[TextMessage],
                                   main_task_manager: Optional[Any] = None,
                                   current_task: Optional[TaskItem] = None) -> List[TextMessage]:
        """
        统一的上下文注入方法，供on_messages和on_messages_stream使用
        """
        if not messages:
            self.logger.warning("收到空消息列表")
            return messages

        if not main_task_manager:
            # 尝试使用存储的引用作为后备
            if self._current_main_task_manager:
                main_task_manager = self._current_main_task_manager
                current_task = self._current_task
                self.logger.debug(f"🔄 {self.name}: 使用存储的main_task_manager引用")
            else:
                # 如果没有main_task_manager，只记录debug信息，不影响功能
                self.logger.debug(f"🔄 {self.name}: 未提供 main_task_manager，跳过上下文注入")
                return messages

        try:
            self.logger.debug("构建统一单轮对话上下文...")

            # 获取Agent的system message
            system_message = self._get_system_message()

            # 获取当前请求内容
            current_request = messages[-1].content if messages else ""

            # 通过任务管理器类型判断主/子任务流程
            if main_task_manager is None:
                # 没有任务管理器，使用简化上下文
                user_message = f"# 当前请求\n{current_request}"
                self.logger.warning(f"⚠️ {self.name}: main_task_manager为None，使用简化上下文")
            else:
                task_manager_type = type(main_task_manager).__name__

                if task_manager_type == 'SubTaskManager':
                    # 子流程：使用SubTaskManager的上下文
                    # 直接使用context_provider构建子流程上下文
                    context_data = {
                        "task_info": {
                            "task_id": getattr(main_task_manager, 'main_task_id', ''),
                            "task_title": getattr(main_task_manager, 'task_title', ''),
                            "parent_session": getattr(main_task_manager, 'parent_session_id', ''),
                            "analysis_progress": {
                                "current_count": getattr(main_task_manager, 'analysis_count', 0),
                                "max_count": getattr(main_task_manager, 'max_analysis_count', 10),
                                "remaining": getattr(main_task_manager, 'max_analysis_count', 10) - getattr(main_task_manager, 'analysis_count', 0)
                            }
                        },
                        "activity_history": getattr(main_task_manager, 'activity_history', [])[-10:],  # 最近10条
                        "context_summary": getattr(main_task_manager, 'context_summary', '')
                    }
                    user_message = self.context_provider.build_sub_flow_context(context_data, current_request)
                    self.logger.debug(f"🔄 {self.name}: 使用子流程上下文 (SubTaskManager)")
                elif task_manager_type == 'MainTaskManager':
                    # 主流程：使用MainTaskManager的上下文
                    if hasattr(main_task_manager, 'get_main_flow_context'):
                        context_data = main_task_manager.get_main_flow_context(self.agent_role.value)
                        user_message = self.context_provider.build_main_flow_context(context_data, current_request)
                        self.logger.debug(f"🔄 {self.name}: 使用主流程上下文 (MainTaskManager)")
                    else:
                        user_message = f"# 当前请求\n{current_request}"
                        self.logger.warning(f"⚠️ {self.name}: MainTaskManager缺少get_main_flow_context方法")
                else:
                    # 降级处理：未知任务管理器类型
                    user_message = f"# 当前请求\n{current_request}"
                    self.logger.warning(f"⚠️ {self.name}: 未知任务管理器类型 {task_manager_type}，使用简化上下文")

            # 获取Agent个人记忆上下文
            agent_memory_context = self.context_provider.get_agent_memory_context(self.name, current_request)

            # 将记忆上下文添加到user消息中
            if agent_memory_context.strip():
                user_message += f"\n\n## Agent个人记忆：\n{agent_memory_context}"

            # 创建单轮对话消息列表
            enhanced_messages = [
                TextMessage(content=system_message, source="System"),
                TextMessage(content=user_message, source="User")
            ]

            self.logger.info(f"🧠 {self.name} 已成功注入上下文 (角色: {self.agent_role.value})")
            return enhanced_messages

        except Exception as e:
            self.logger.error(f"❌ {self.name}: 上下文注入失败: {e}。将使用原始消息继续。")
            return messages

    async def on_messages(self,
                          messages: List[TextMessage],
                          cancellation_token: CancellationToken,
                          main_task_manager: Optional[Any] = None,
                          current_task: Optional[TaskItem] = None) -> Any:
        """
        处理消息时，自动注入由ContextProvider生成的结构化上下文，并详细记录LLM交互。
        """
        self.logger.info(f"Agent {self.name} 开始处理消息")
        self.logger.debug(f"输入消息数量: {len(messages)}")

        # 存储引用供后续使用
        self._current_main_task_manager = main_task_manager
        self._current_task = current_task

        # 注入上下文
        enhanced_messages = self._inject_context_to_messages(messages, main_task_manager, current_task)

        # 获取模型名称
        model_name = 'unknown_model'
        model_client = getattr(self, 'model_client', None) or getattr(self, '_model_client', None)
        if model_client:
            if hasattr(model_client, 'model'):
                model_name = model_client.model
            elif hasattr(model_client, 'model_info'):
                model_info = model_client.model_info
                if isinstance(model_info, dict) and 'model' in model_info:
                    model_name = model_info['model']

        # 打印LLM请求信息
        request_content = ""
        for msg in enhanced_messages:
            request_content += f"[{msg.source}]: {msg.content}\n"

        print_llm_interaction(
            agent_name=self.name,
            model_name=model_name,
            interaction_type="REQUEST",
            content=request_content,
            message_count=len(enhanced_messages)
        )

        # 调用原始的 on_messages 处理
        response = await super().on_messages(enhanced_messages, cancellation_token)

        # 打印LLM响应信息
        response_content = ""
        if hasattr(response, 'chat_message') and response.chat_message:
            response_content = getattr(response.chat_message, 'content', str(response.chat_message))
        else:
            response_content = getattr(response, 'content', str(response))

        print_llm_interaction(
            agent_name=self.name,
            model_name=model_name,
            interaction_type="RESPONSE",
            content=response_content,
            response_type=type(response).__name__
        )

        return response

    async def on_messages_stream(self,
                                messages: List[TextMessage],
                                cancellation_token: CancellationToken,
                                main_task_manager: Optional[Any] = None,
                                current_task: Optional[TaskItem] = None) -> AsyncIterator[Any]:
        """
        流式处理消息时，自动注入由ContextProvider生成的结构化上下文，并详细记录LLM交互。
        """
        self.logger.info(f"Agent {self.name} 开始流式处理消息")
        self.logger.debug(f"输入消息数量: {len(messages)}")

        # 存储引用供后续使用
        self._current_main_task_manager = main_task_manager
        self._current_task = current_task

        # 注入上下文
        enhanced_messages = self._inject_context_to_messages(messages, main_task_manager, current_task)

        # 获取模型名称
        model_name = 'unknown_model'
        model_client = getattr(self, 'model_client', None) or getattr(self, '_model_client', None)
        if model_client:
            if hasattr(model_client, 'model'):
                model_name = model_client.model
            elif hasattr(model_client, 'model_info'):
                model_info = model_client.model_info
                if isinstance(model_info, dict) and 'model' in model_info:
                    model_name = model_info['model']

        # 打印LLM请求信息
        request_content = ""
        for msg in enhanced_messages:
            request_content += f"[{msg.source}]: {msg.content}\n"

        print_llm_interaction(
            agent_name=self.name,
            model_name=model_name,
            interaction_type="STREAM_REQUEST",
            content=request_content,
            message_count=len(enhanced_messages)
        )

        # 收集流式响应
        collected_responses = []
        reasoning_parts = []
        tool_calls = []
        final_message = None

        # 调用原始的 on_messages_stream 处理
        try:
            stream_result = super().on_messages_stream(enhanced_messages, cancellation_token)
            if stream_result is not None:
                async for response in stream_result:
                    collected_responses.append(response)

                    # 收集不同类型的响应内容
                    inner_messages = getattr(response, 'inner_messages', None)
                    if inner_messages:
                        for inner_msg in inner_messages:
                            content = getattr(inner_msg, 'content', None)
                            if content and 'thinking' in str(content).lower():
                                reasoning_parts.append(str(content))

                    # 检查工具调用（在inner_messages中）
                    if inner_messages:
                        for inner_msg in inner_messages:
                            content = getattr(inner_msg, 'content', None)
                            if content and isinstance(content, list):
                                tool_calls.extend(content)

                    chat_message = getattr(response, 'chat_message', None)
                    if chat_message:
                        final_message = getattr(chat_message, 'content', None)

                    yield response
        finally:
            # 打印流式响应汇总
            if collected_responses:
                summary_content = ""

                if reasoning_parts:
                    summary_content += f"推理过程: {len(reasoning_parts)} 部分\n"
                    for i, reasoning in enumerate(reasoning_parts[:2]):  # 只显示前2个推理部分
                        preview = reasoning[:200] + "..." if len(reasoning) > 200 else reasoning
                        summary_content += f"推理{i+1}: {preview}\n"

                if tool_calls:
                    summary_content += f"\n工具调用: {len(tool_calls)} 个\n"
                    for i, tool_call in enumerate(tool_calls):
                        tool_name = getattr(tool_call, 'name', 'unknown_tool')
                        summary_content += f"工具{i+1}: {tool_name}\n"

                if final_message:
                    summary_content += f"\n最终回复:\n{final_message}"

                print_llm_interaction(
                    agent_name=self.name,
                    model_name=model_name,
                    interaction_type="STREAM_RESPONSE",
                    content=summary_content,
                    total_responses=len(collected_responses),
                    reasoning_count=len(reasoning_parts),
                    tool_call_count=len(tool_calls)
                )

            # 清理引用
            self._current_main_task_manager = None
            self._current_task = None

