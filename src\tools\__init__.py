"""
工具模块初始化

新的工具系统基于记忆管理工具、信息收集工具和执行工具
"""

# 导入新的工具系统
from .memory_tools import get_memory_tool_functions
from .information_collection_tools import get_information_collection_tool_functions
from .execution_tools import get_execution_tool_functions
from .threat_intelligence_tools import get_threat_intelligence_tool_functions

def get_all_tool_functions():
    """获取所有工具函数，包括记忆管理工具、信息收集工具、执行工具和威胁情报工具"""
    tools = []
    tools.extend(get_memory_tool_functions())
    tools.extend(get_information_collection_tool_functions())
    tools.extend(get_execution_tool_functions())
    tools.extend(get_threat_intelligence_tool_functions())
    return tools

__all__ = [
    "get_memory_tool_functions",
    "get_information_collection_tool_functions",
    "get_execution_tool_functions",
    "get_threat_intelligence_tool_functions",
    "get_all_tool_functions"
]