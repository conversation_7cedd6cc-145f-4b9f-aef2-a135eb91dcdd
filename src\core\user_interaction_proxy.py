"""
用户交互代理

负责在分析流程中处理用户交互，包括：
1. 用户确认和输入
2. 进度展示
3. 结果展示
4. 错误处理
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from src.core.user_interface import get_ui_manager, UIType, MessageType, ProgressInfo
from src.core.analysis_flow_controller import AnalysisFlowController, AnalysisSession
from src.core.task_manager import MainTaskManager


class UserInteractionProxy:
    """用户交互代理"""
    
    def __init__(self):
        self.ui_manager = get_ui_manager()
        self.current_ui = None
        self.analysis_controller: Optional[AnalysisFlowController] = None
        
    async def initialize(self, ui_type: UIType = UIType.CLI) -> bool:
        """初始化用户交互代理"""
        try:
            # 设置用户界面
            success = await self.ui_manager.set_active_interface(ui_type)
            if not success:
                return False
            
            self.current_ui = await self.ui_manager.get_current_interface()
            if not self.current_ui:
                return False
            
            await self.current_ui.success("用户交互代理初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 用户交互代理初始化失败: {e}")
            return False
    
    def set_analysis_controller(self, controller: AnalysisFlowController):
        """设置分析流程控制器"""
        self.analysis_controller = controller
    
    async def start_interactive_analysis(self) -> Tuple[bool, Optional[str]]:
        """开始交互式分析"""
        if not self.current_ui:
            return False, "用户界面未初始化"
        
        if not self.analysis_controller:
            return False, "分析控制器未设置"
        
        try:
            # 欢迎信息
            await self.current_ui.info(
                "欢迎使用网络安全多智能体分析系统",
                "系统启动"
            )
            
            # 获取用户分析请求
            user_request = await self._get_analysis_request()
            if not user_request:
                return False, "用户取消分析"
            
            # 确认分析请求
            confirmed = await self._confirm_analysis_request(user_request)
            if not confirmed:
                return False, "用户取消分析"
            
            # 开始分析
            await self.current_ui.info("开始执行安全分析...", "分析启动")
            
            # 启动分析流程（带进度监控）
            success, error = await self._execute_analysis_with_progress(user_request)
            
            if success:
                await self._display_analysis_completion()
                return True, None
            else:
                await self.current_ui.error(f"分析失败: {error}", "分析错误")
                return False, error
                
        except Exception as e:
            error_msg = f"交互式分析失败: {str(e)}"
            if self.current_ui:
                await self.current_ui.error(error_msg, "系统错误")
            return False, error_msg
    
    async def _get_analysis_request(self) -> Optional[str]:
        """获取用户分析请求"""
        assert self.current_ui
        try:
            await self.current_ui.info(
                "请描述您需要进行的安全分析任务。\n"
                "例如：\n"
                "- 分析可疑IP地址 *************\n"
                "- 检测网络中的恶意活动\n"
                "- 分析日志文件中的异常行为\n"
                "- 评估系统的安全漏洞",
                "分析请求"
            )
            
            user_request = await self.current_ui.ask_text(
                "请输入您的分析请求",
                required=True
            )
            
            return user_request
            
        except Exception as e:
            await self.current_ui.error(f"获取分析请求失败: {e}")
            return None
    
    async def _confirm_analysis_request(self, user_request: str) -> bool:
        """确认分析请求"""
        assert self.current_ui
        try:
            await self.current_ui.info(
                f"您的分析请求：\n{user_request}",
                "请求确认"
            )
            
            confirmed = await self.current_ui.ask_confirm(
                "确认开始分析吗？",
                default=True
            )
            
            return confirmed
            
        except Exception as e:
            await self.current_ui.error(f"确认分析请求失败: {e}")
            return False
    
    async def _execute_analysis_with_progress(self, user_request: str) -> Tuple[bool, Optional[str]]:
        """执行分析并显示进度"""
        assert self.analysis_controller
        try:
            # 创建进度监控任务
            progress_task = asyncio.create_task(self._monitor_analysis_progress())
            
            # 执行分析
            analysis_task = asyncio.create_task(
                self.analysis_controller.start_analysis(user_request)
            )
            
            # 等待分析完成
            success, error = await analysis_task
            
            # 停止进度监控
            progress_task.cancel()
            
            return success, error
            
        except Exception as e:
            return False, str(e)
    
    async def _monitor_analysis_progress(self):
        """监控分析进度"""
        assert self.current_ui
        try:
            step_names = [
                "记忆查找",
                "计划制定", 
                "任务执行",
                "报告生成",
                "知识存储"
            ]
            
            while True:
                if not self.analysis_controller or not self.analysis_controller.current_session:
                    await asyncio.sleep(1)
                    continue
                
                session = self.analysis_controller.current_session
                current_phase = session.current_phase
                
                # 根据当前阶段显示进度
                if hasattr(current_phase, 'value'):
                    phase_name = current_phase.value
                    
                    # 简单的进度映射
                    phase_progress = {
                        "memory_search": (1, "记忆查找"),
                        "planning": (2, "计划制定"),
                        "main_task_execution": (3, "任务执行"),
                        "report_generation": (4, "报告生成"),
                        "knowledge_storage": (5, "知识存储"),
                        "completed": (5, "分析完成")
                    }
                    
                    if phase_name in phase_progress:
                        current, message = phase_progress[phase_name]
                        progress = ProgressInfo(
                            current=current,
                            total=5,
                            message=message
                        )
                        await self.current_ui.show_progress(progress)
                
                # 显示任务状态
                if session.main_task_manager:
                    task_status = self._get_task_status_summary(session.main_task_manager)
                    await self.current_ui.display_task_status(task_status)
                
                await asyncio.sleep(2)  # 每2秒更新一次
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            await self.current_ui.warning(f"进度监控错误: {e}")
    
    def _get_task_status_summary(self, task_manager: MainTaskManager) -> Dict[str, Any]:
        """获取任务状态摘要"""
        summary = task_manager.get_task_status_summary()

        return {
            "current_phase": "任务执行中",
            "progress": {
                "total": summary.get("total_tasks", 0),
                "completed": summary.get("status_counts", {}).get("completed", 0),
                "in_progress": summary.get("status_counts", {}).get("in_progress", 0),
                "pending": summary.get("status_counts", {}).get("pending", 0),
                "progress_percentage": summary.get("progress_percentage", 0)
            },
            "analysis_progress": summary.get("analysis_progress", {}),
            "current_task": "正在执行安全分析任务"
        }
    
    async def _display_analysis_completion(self):
        """显示分析完成信息"""
        assert self.current_ui
        assert self.analysis_controller
        try:
            if not self.analysis_controller or not self.analysis_controller.current_session:
                return
            
            session = self.analysis_controller.current_session
            
            # 显示完成消息
            await self.current_ui.success(
                "安全分析已完成！",
                "分析完成"
            )
            
            # 显示分析结果
            result_data = {
                "session_id": session.session_id,
                "final_report": session.final_report,
            }
            
            if session.main_task_manager:
                task_summary = session.main_task_manager.get_task_status_summary()
                result_data["task_summary"] = {
                    "total_tasks": task_summary.get("total_tasks", 0),
                    "completed": task_summary.get("status_counts", {}).get("completed", 0),
                    "failed": task_summary.get("status_counts", {}).get("failed", 0),
                    "progress_percentage": task_summary.get("progress_percentage", 0),
                    "has_final_report": task_summary.get("has_final_report", False)
                }
            
            await self.current_ui.display_analysis_result(result_data)
            
            # 询问是否保存结果
            save_result = await self.current_ui.ask_confirm(
                "是否保存分析结果到文件？",
                default=True
            )
            
            if save_result:
                await self._save_analysis_result(session)
            
        except Exception as e:
            await self.current_ui.error(f"显示分析完成信息失败: {e}")
    
    async def _save_analysis_result(self, session: AnalysisSession):
        """保存分析结果"""
        assert self.current_ui
        try:
            filename = f"analysis_result_{session.session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"安全分析结果报告\n")
                f.write(f"=" * 50 + "\n")
                f.write(f"会话ID: {session.session_id}\n")
                f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"用户请求: {session.user_request}\n")
                f.write(f"\n分析报告:\n")
                f.write(f"-" * 30 + "\n")
                f.write(session.final_report or "无分析报告")
                f.write(f"\n" + "=" * 50 + "\n")
            
            await self.current_ui.success(
                f"分析结果已保存到文件: {filename}",
                "保存成功"
            )
            
        except Exception as e:
            await self.current_ui.error(f"保存分析结果失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        if self.ui_manager:
            await self.ui_manager.cleanup_all()


async def run_interactive_analysis(analysis_controller: AnalysisFlowController) -> Tuple[bool, Optional[str]]:
    """运行交互式分析的便捷函数"""
    proxy = UserInteractionProxy()
    
    # 初始化用户界面
    if not await proxy.initialize():
        return False, "用户界面初始化失败"
    
    # 设置分析控制器
    proxy.set_analysis_controller(analysis_controller)
    
    try:
        # 开始交互式分析
        return await proxy.start_interactive_analysis()
    finally:
        # 清理资源
        await proxy.cleanup()
